"""
Test script for portfolio dashboard
"""
import requests
import json
import time

def test_portfolio_dashboard():
    """Test portfolio dashboard functionality"""
    base_url = "http://localhost:8000/api/v1"
    
    print("🧪 Testing Portfolio Dashboard")
    print("=" * 50)
    
    # Test 1: Get current portfolio
    print("1. Testing current portfolio...")
    try:
        response = requests.get(f"{base_url}/portfolio")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Portfolio loaded successfully")
            print(f"   💰 Total Invested: ${data.get('total_invested', 0):,.2f}")
            print(f"   📊 Current Value: ${data.get('current_value', 0):,.2f}")
            print(f"   📈 Total P&L: ${data.get('total_pnl', 0):,.2f}")
            print(f"   📊 P&L %: {data.get('total_pnl_percentage', 0):.2f}%")
            print(f"   🔢 Positions: {len(data.get('positions', []))}")
            
            # Show positions
            for pos in data.get('positions', []):
                pnl_emoji = "📈" if pos['pnl'] >= 0 else "📉"
                print(f"      {pnl_emoji} {pos['symbol']}: {pos['quantity']} @ ${pos['avg_buy_price']:,} = ${pos['current_value']:,.2f} ({pos['pnl_percentage']:+.2f}%)")
                
        else:
            print(f"   ❌ Portfolio failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Portfolio error: {e}")
    
    # Test 2: Add a new position
    print("\n2. Testing add position...")
    try:
        new_position = {
            "symbol": "ADAUSDT",
            "quantity": 1000,
            "buy_price": 1.5
        }
        
        response = requests.post(
            f"{base_url}/portfolio/add",
            params=new_position
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Position added: {new_position['quantity']} {new_position['symbol']} at ${new_position['buy_price']}")
        else:
            print(f"   ❌ Add position failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Add position error: {e}")
    
    # Test 3: Get updated portfolio
    print("\n3. Testing updated portfolio...")
    try:
        response = requests.get(f"{base_url}/portfolio")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Updated portfolio:")
            print(f"   💰 Total Invested: ${data.get('total_invested', 0):,.2f}")
            print(f"   📊 Current Value: ${data.get('current_value', 0):,.2f}")
            print(f"   📈 Total P&L: ${data.get('total_pnl', 0):,.2f}")
            print(f"   🔢 Positions: {len(data.get('positions', []))}")
            
        else:
            print(f"   ❌ Updated portfolio failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Updated portfolio error: {e}")
    
    # Test 4: Test dashboard accessibility
    print("\n4. Testing dashboard accessibility...")
    try:
        response = requests.get("http://localhost:8000/static/portfolio-dashboard.html")
        if response.status_code == 200:
            print(f"   ✅ Portfolio dashboard accessible")
            print(f"   📄 Content length: {len(response.text):,} characters")
        else:
            print(f"   ❌ Dashboard not accessible: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Dashboard accessibility error: {e}")
    
    # Test 5: Remove position
    print("\n5. Testing remove position...")
    try:
        response = requests.delete(f"{base_url}/portfolio/remove?symbol=ADAUSDT")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Position removed: ADAUSDT")
        else:
            print(f"   ❌ Remove position failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Remove position error: {e}")

def show_portfolio_features():
    """Show portfolio dashboard features"""
    print("\n🎯 Portfolio Dashboard Features")
    print("=" * 50)
    
    features = [
        "💼 Portfolio Summary - Total invested, current value, P&L",
        "📊 Position Tracking - Individual asset performance",
        "📈 Performance Charts - Portfolio value over time",
        "🥧 Asset Allocation - Pie chart of holdings",
        "➕ Add Positions - Easy position management",
        "🗑️ Remove Positions - Position cleanup",
        "📱 Responsive Design - Mobile-friendly interface",
        "🔄 Auto Refresh - Real-time updates",
        "📤 Export Data - CSV export functionality",
        "⚖️ Rebalancing - Portfolio optimization (coming soon)",
        "📊 Analytics - Advanced portfolio metrics",
        "🎨 Dark Theme - Professional trading interface"
    ]
    
    for feature in features:
        print(f"   {feature}")
    
    print("\n🌐 Dashboard URLs:")
    print("   Portfolio: http://localhost:8000/static/portfolio-dashboard.html")
    print("   Main Dashboard: http://localhost:8000")
    print("   Advanced Charts: http://localhost:8000/static/advanced-dashboard.html")

def main():
    """Run portfolio dashboard tests"""
    print("🚀 Portfolio Dashboard Test Suite")
    print("Testing portfolio management functionality")
    print("=" * 70)
    
    # Wait for server to be ready
    time.sleep(2)
    
    # Run tests
    test_portfolio_dashboard()
    show_portfolio_features()
    
    print("\n" + "=" * 70)
    print("🎉 Portfolio dashboard testing completed!")
    print("\n💡 Next Steps:")
    print("   1. Open http://localhost:8000/static/portfolio-dashboard.html")
    print("   2. Add some positions using the + Add Position button")
    print("   3. View your portfolio performance and allocation")
    print("   4. Export your data or view analytics")

if __name__ == "__main__":
    main()
