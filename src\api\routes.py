"""
FastAPI routes for the crypto trading system
"""
from fastapi import APIRouter, HTTPException, Query
from typing import List, Optional
from datetime import datetime, timedelta
import logging

from src.api.models import (
    PriceData, TechnicalSignals, MarketOverview, SymbolInfo,
    ChartData, SystemStatus, ErrorResponse, PortfolioSummary,
    PortfolioItem, AlertItem, AlertCreate, AdvancedChartData,
    CandlestickData, TechnicalIndicatorLine
)
from src.database.database import get_db_session
from src.database.models import CryptoPriceData, Portfolio, PriceAlert
from src.technical_analysis.analyzer import TechnicalAnalyzer
from src.data_collection.binance_collector import BinanceCollector
from src.portfolio.manager import PortfolioManager
from src.alerts.manager import AlertManager
from config.settings import settings

logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

@router.get("/", summary="API Health Check")
async def root():
    """API health check endpoint"""
    return {
        "message": "Crypto Trading System API",
        "version": "1.0.0",
        "status": "running",
        "timestamp": datetime.now()
    }

@router.get("/status", response_model=SystemStatus, summary="System Status")
async def get_system_status():
    """Get system status and statistics"""
    try:
        db = get_db_session()

        # Get database statistics
        total_records = db.query(CryptoPriceData).count()
        symbols_count = db.query(CryptoPriceData.symbol).distinct().count()

        # Get latest data timestamp
        latest_record = db.query(CryptoPriceData).order_by(
            CryptoPriceData.timestamp.desc()
        ).first()

        last_data_update = latest_record.timestamp if latest_record else None

        # Check if indicators are updated (check if any RSI values exist)
        indicators_count = db.query(CryptoPriceData).filter(
            CryptoPriceData.rsi.isnot(None)
        ).count()

        indicators_updated = indicators_count > 0

        db.close()

        return SystemStatus(
            status="healthy",
            total_records=total_records,
            symbols_count=symbols_count,
            last_data_update=last_data_update,
            indicators_updated=indicators_updated,
            uptime="Running"
        )

    except Exception as e:
        logger.error(f"Error getting system status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/symbols", response_model=List[str], summary="Get Available Symbols")
async def get_symbols():
    """Get list of available trading symbols"""
    try:
        db = get_db_session()
        symbols = db.query(CryptoPriceData.symbol).distinct().all()
        db.close()

        return [symbol[0] for symbol in symbols]

    except Exception as e:
        logger.error(f"Error getting symbols: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/market", response_model=MarketOverview, summary="Market Overview")
async def get_market_overview():
    """Get market overview with current prices"""
    try:
        collector = BinanceCollector()
        symbols_info = []

        for symbol in settings.supported_symbols:
            try:
                current_price = collector.get_current_price(symbol)
                ticker_24h = collector.get_24h_ticker(symbol)

                if current_price:
                    symbol_info = SymbolInfo(
                        symbol=symbol,
                        current_price=current_price,
                        price_change_24h=ticker_24h.get('price_change_percent') if ticker_24h else None,
                        volume_24h=ticker_24h.get('volume') if ticker_24h else None,
                        last_update=datetime.now()
                    )
                    symbols_info.append(symbol_info)

            except Exception as e:
                logger.warning(f"Error getting data for {symbol}: {e}")
                continue

        return MarketOverview(
            total_symbols=len(symbols_info),
            symbols=symbols_info,
            last_update=datetime.now()
        )

    except Exception as e:
        logger.error(f"Error getting market overview: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/price/{symbol}", response_model=List[PriceData], summary="Get Price Data")
async def get_price_data(
    symbol: str,
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    include_indicators: bool = Query(True, description="Include technical indicators")
):
    """Get historical price data for a symbol"""
    try:
        db = get_db_session()

        query = db.query(CryptoPriceData).filter(
            CryptoPriceData.symbol == symbol.upper()
        ).order_by(CryptoPriceData.timestamp.desc()).limit(limit)

        records = query.all()

        if not records:
            raise HTTPException(status_code=404, detail=f"No data found for symbol {symbol}")

        price_data = []
        for record in reversed(records):  # Reverse to get chronological order
            data = PriceData(
                timestamp=record.timestamp,
                open=record.open_price,
                high=record.high_price,
                low=record.low_price,
                close=record.close_price,
                volume=record.volume
            )

            if include_indicators:
                data.rsi = record.rsi
                data.macd = record.macd
                data.macd_signal = record.macd_signal
                data.bb_upper = record.bb_upper
                data.bb_middle = record.bb_middle
                data.bb_lower = record.bb_lower

            price_data.append(data)

        db.close()
        return price_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting price data for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/signals/{symbol}", response_model=TechnicalSignals, summary="Get Trading Signals")
async def get_trading_signals(symbol: str):
    """Get current trading signals for a symbol"""
    try:
        analyzer = TechnicalAnalyzer()
        signals = analyzer.analyze_signals(symbol.upper())

        if "error" in signals:
            raise HTTPException(status_code=404, detail=signals["error"])

        return TechnicalSignals(
            symbol=signals["symbol"],
            timestamp=signals["timestamp"],
            price=signals["price"],
            signals=signals["signals"]
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting signals for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/chart/{symbol}", response_model=ChartData, summary="Get Chart Data")
async def get_chart_data(
    symbol: str,
    timeframe: str = Query("1h", description="Timeframe (1m, 5m, 15m, 1h, 4h, 1d)"),
    limit: int = Query(200, ge=1, le=1000, description="Number of data points")
):
    """Get chart data with prices and indicators"""
    try:
        # For now, we'll return minute data regardless of timeframe
        # In a production system, you'd aggregate data based on timeframe

        db = get_db_session()

        records = db.query(CryptoPriceData).filter(
            CryptoPriceData.symbol == symbol.upper()
        ).order_by(CryptoPriceData.timestamp.desc()).limit(limit).all()

        if not records:
            raise HTTPException(status_code=404, detail=f"No data found for symbol {symbol}")

        prices = []
        indicators = []

        for record in reversed(records):
            price_data = PriceData(
                timestamp=record.timestamp,
                open=record.open_price,
                high=record.high_price,
                low=record.low_price,
                close=record.close_price,
                volume=record.volume,
                rsi=record.rsi,
                macd=record.macd,
                macd_signal=record.macd_signal,
                bb_upper=record.bb_upper,
                bb_middle=record.bb_middle,
                bb_lower=record.bb_lower
            )
            prices.append(price_data)

        db.close()

        return ChartData(
            symbol=symbol.upper(),
            timeframe=timeframe,
            prices=prices,
            indicators=[]  # Indicators are included in prices for now
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting chart data for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Portfolio Management Endpoints
@router.get("/portfolio", summary="Get Portfolio Summary")
async def get_portfolio(user_id: str = "default"):
    """Get portfolio summary with current values"""
    try:
        portfolio_manager = PortfolioManager(user_id)
        summary = portfolio_manager.get_portfolio_summary()
        return summary

    except Exception as e:
        logger.error(f"Error getting portfolio: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/portfolio/add", summary="Add Position to Portfolio")
async def add_position(
    symbol: str,
    quantity: float,
    buy_price: float,
    user_id: str = "default"
):
    """Add a new position to the portfolio"""
    try:
        portfolio_manager = PortfolioManager(user_id)
        success = portfolio_manager.add_position(symbol.upper(), quantity, buy_price)

        if success:
            return {"message": f"Added {quantity} {symbol} at ${buy_price}", "success": True}
        else:
            raise HTTPException(status_code=400, detail="Failed to add position")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding position: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/portfolio/remove", summary="Remove Position from Portfolio")
async def remove_position(
    symbol: str,
    quantity: Optional[float] = None,
    user_id: str = "default"
):
    """Remove or reduce a position from the portfolio"""
    try:
        portfolio_manager = PortfolioManager(user_id)
        success = portfolio_manager.remove_position(symbol.upper(), quantity)

        if success:
            message = f"Removed {quantity if quantity else 'all'} {symbol}"
            return {"message": message, "success": True}
        else:
            raise HTTPException(status_code=404, detail="Position not found")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error removing position: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Alert Management Endpoints
@router.get("/alerts", summary="Get All Alerts")
async def get_alerts(user_id: str = "default", active_only: bool = False):
    """Get all alerts for the user"""
    try:
        alert_manager = AlertManager(user_id)
        alerts = alert_manager.get_alerts(active_only)
        return {"alerts": alerts, "count": len(alerts)}

    except Exception as e:
        logger.error(f"Error getting alerts: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/alerts/create", summary="Create New Alert")
async def create_alert(
    symbol: str,
    alert_type: str,
    target_value: float,
    message: Optional[str] = None,
    user_id: str = "default"
):
    """Create a new price alert"""
    try:
        # Validate alert type
        valid_types = ["price_above", "price_below", "rsi_overbought", "rsi_oversold"]
        if alert_type not in valid_types:
            raise HTTPException(status_code=400, detail=f"Invalid alert type. Must be one of: {valid_types}")

        # Validate target value based on alert type
        if alert_type in ["rsi_overbought", "rsi_oversold"]:
            if target_value < 0 or target_value > 100:
                raise HTTPException(status_code=400, detail="RSI value must be between 0 and 100")
        elif alert_type in ["price_above", "price_below"]:
            if target_value <= 0:
                raise HTTPException(status_code=400, detail="Price must be greater than 0")

        # Validate symbol
        valid_symbols = ["BTCUSDT", "ETHUSDT", "BNBUSDT", "ADAUSDT", "XRPUSDT",
                        "SOLUSDT", "DOTUSDT", "DOGEUSDT", "AVAXUSDT", "LINKUSDT"]
        if symbol.upper() not in valid_symbols:
            raise HTTPException(status_code=400, detail=f"Invalid symbol. Must be one of: {valid_symbols}")

        alert_manager = AlertManager(user_id)
        success = alert_manager.create_alert(symbol.upper(), alert_type, target_value, message)

        if success:
            return {"message": f"Alert created: {alert_type} for {symbol}", "success": True}
        else:
            raise HTTPException(status_code=400, detail="Failed to create alert")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating alert: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/alerts/{alert_id}", summary="Delete Alert")
async def delete_alert(alert_id: int, user_id: str = "default"):
    """Delete an alert"""
    try:
        alert_manager = AlertManager(user_id)
        success = alert_manager.delete_alert(alert_id)

        if success:
            return {"message": f"Alert {alert_id} deleted", "success": True}
        else:
            raise HTTPException(status_code=404, detail="Alert not found")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting alert: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/alerts/{alert_id}/toggle", summary="Toggle Alert")
async def toggle_alert(alert_id: int, user_id: str = "default"):
    """Toggle alert active status"""
    try:
        alert_manager = AlertManager(user_id)
        success = alert_manager.toggle_alert(alert_id)

        if success:
            return {"message": f"Alert {alert_id} toggled", "success": True}
        else:
            raise HTTPException(status_code=404, detail="Alert not found")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error toggling alert: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/alerts/check", summary="Check All Alerts")
async def check_alerts(user_id: str = "default"):
    """Check all active alerts and return triggered ones"""
    try:
        alert_manager = AlertManager(user_id)
        triggered = alert_manager.check_alerts()

        return {
            "triggered_alerts": triggered,
            "count": len(triggered),
            "timestamp": datetime.now()
        }

    except Exception as e:
        logger.error(f"Error checking alerts: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/alerts/types", summary="Get Alert Types")
async def get_alert_types():
    """Get available alert types"""
    try:
        alert_manager = AlertManager()
        types = alert_manager.get_alert_types()
        return {"alert_types": types}

    except Exception as e:
        logger.error(f"Error getting alert types: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/update/indicators", summary="Update Technical Indicators")
async def update_indicators(symbols: Optional[List[str]] = None):
    """Update technical indicators for specified symbols or all symbols"""
    try:
        analyzer = TechnicalAnalyzer()

        if symbols:
            # Update specific symbols
            symbols = [s.upper() for s in symbols]
            results = analyzer.update_all_symbols(symbols)
        else:
            # Update all symbols
            results = analyzer.update_all_symbols()

        successful = sum(1 for result in results.values() if result == "success")
        total = len(results)

        return {
            "message": f"Updated indicators for {successful}/{total} symbols",
            "results": results,
            "timestamp": datetime.now()
        }

    except Exception as e:
        logger.error(f"Error updating indicators: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/advanced-chart/{symbol}", response_model=AdvancedChartData, summary="Get Advanced Chart Data")
async def get_advanced_chart_data(
    symbol: str,
    timeframe: str = Query("1h", description="Timeframe (1m, 5m, 15m, 1h, 4h, 1d)"),
    limit: int = Query(500, ge=1, le=2000, description="Number of data points")
):
    """Get advanced chart data with candlesticks and all technical indicators"""
    try:
        db = get_db_session()

        records = db.query(CryptoPriceData).filter(
            CryptoPriceData.symbol == symbol.upper()
        ).order_by(CryptoPriceData.timestamp.desc()).limit(limit).all()

        if not records:
            raise HTTPException(status_code=404, detail=f"No data found for symbol {symbol}")

        # Reverse to get chronological order
        records = list(reversed(records))

        candlesticks = []
        rsi_data = []
        macd_data = []
        macd_signal_data = []
        bb_upper_data = []
        bb_middle_data = []
        bb_lower_data = []
        volume_data = []

        for record in records:
            candlesticks.append(CandlestickData(
                timestamp=record.timestamp,
                open=record.open_price,
                high=record.high_price,
                low=record.low_price,
                close=record.close_price,
                volume=record.volume
            ))

            rsi_data.append(TechnicalIndicatorLine(
                timestamp=record.timestamp,
                value=record.rsi
            ))

            macd_data.append(TechnicalIndicatorLine(
                timestamp=record.timestamp,
                value=record.macd
            ))

            macd_signal_data.append(TechnicalIndicatorLine(
                timestamp=record.timestamp,
                value=record.macd_signal
            ))

            bb_upper_data.append(TechnicalIndicatorLine(
                timestamp=record.timestamp,
                value=record.bb_upper
            ))

            bb_middle_data.append(TechnicalIndicatorLine(
                timestamp=record.timestamp,
                value=record.bb_middle
            ))

            bb_lower_data.append(TechnicalIndicatorLine(
                timestamp=record.timestamp,
                value=record.bb_lower
            ))

            volume_data.append(TechnicalIndicatorLine(
                timestamp=record.timestamp,
                value=record.volume
            ))

        db.close()

        return AdvancedChartData(
            symbol=symbol.upper(),
            timeframe=timeframe,
            candlesticks=candlesticks,
            rsi=rsi_data,
            macd=macd_data,
            macd_signal=macd_signal_data,
            bb_upper=bb_upper_data,
            bb_middle=bb_middle_data,
            bb_lower=bb_lower_data,
            volume=volume_data
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting advanced chart data for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
