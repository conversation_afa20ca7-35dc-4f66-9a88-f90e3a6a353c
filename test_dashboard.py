"""
Test script for the web dashboard and API
"""
import requests
import json
import time

def test_api_endpoints():
    """Test all API endpoints"""
    base_url = "http://localhost:8000/api/v1"
    
    print("🧪 Testing Web Dashboard API")
    print("=" * 50)
    
    # Test 1: Health check
    print("1. Testing health check...")
    try:
        response = requests.get(f"{base_url}/")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ API Status: {data.get('status', 'unknown')}")
        else:
            print(f"   ❌ Health check failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Health check error: {e}")
    
    # Test 2: System status
    print("\n2. Testing system status...")
    try:
        response = requests.get(f"{base_url}/status")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ System Status: {data.get('status')}")
            print(f"   📊 Total Records: {data.get('total_records'):,}")
            print(f"   🔢 Symbols Count: {data.get('symbols_count')}")
            print(f"   📈 Indicators Updated: {data.get('indicators_updated')}")
        else:
            print(f"   ❌ Status check failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Status check error: {e}")
    
    # Test 3: Get symbols
    print("\n3. Testing symbols endpoint...")
    try:
        response = requests.get(f"{base_url}/symbols")
        if response.status_code == 200:
            symbols = response.json()
            print(f"   ✅ Found {len(symbols)} symbols: {', '.join(symbols[:5])}...")
        else:
            print(f"   ❌ Symbols check failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Symbols check error: {e}")
    
    # Test 4: Market overview
    print("\n4. Testing market overview...")
    try:
        response = requests.get(f"{base_url}/market")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Market data loaded: {data.get('total_symbols')} symbols")
            if data.get('symbols'):
                btc = next((s for s in data['symbols'] if s['symbol'] == 'BTCUSDT'), None)
                if btc:
                    print(f"   💰 BTC Price: ${btc['current_price']:,.2f}")
        else:
            print(f"   ❌ Market overview failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Market overview error: {e}")
    
    # Test 5: Price data
    print("\n5. Testing price data...")
    try:
        response = requests.get(f"{base_url}/price/BTCUSDT?limit=10")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Price data loaded: {len(data)} records")
            if data:
                latest = data[-1]
                print(f"   📊 Latest Close: ${latest['close']:,.2f}")
                print(f"   📈 RSI: {latest.get('rsi', 'N/A')}")
        else:
            print(f"   ❌ Price data failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Price data error: {e}")
    
    # Test 6: Trading signals
    print("\n6. Testing trading signals...")
    try:
        response = requests.get(f"{base_url}/signals/BTCUSDT")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Signals loaded for {data.get('symbol')}")
            print(f"   💰 Current Price: ${data.get('price'):,.2f}")
            signals = data.get('signals', {})
            for indicator, signal in signals.items():
                print(f"   📊 {indicator.upper()}: {signal}")
        else:
            print(f"   ❌ Trading signals failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Trading signals error: {e}")
    
    # Test 7: Chart data
    print("\n7. Testing chart data...")
    try:
        response = requests.get(f"{base_url}/chart/BTCUSDT?limit=50")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Chart data loaded: {len(data.get('prices', []))} data points")
            print(f"   📊 Symbol: {data.get('symbol')}")
            print(f"   ⏰ Timeframe: {data.get('timeframe')}")
        else:
            print(f"   ❌ Chart data failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Chart data error: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 API testing completed!")
    print("\n📊 Dashboard URLs:")
    print("   Main Dashboard: http://localhost:8000")
    print("   API Docs: http://localhost:8000/docs")
    print("   API Status: http://localhost:8000/api/v1/status")

def main():
    """Main test function"""
    print("🚀 Crypto Trading System - Dashboard Test")
    print("Waiting for server to be ready...")
    
    # Wait a moment for server to be ready
    time.sleep(2)
    
    # Test API endpoints
    test_api_endpoints()

if __name__ == "__main__":
    main()
