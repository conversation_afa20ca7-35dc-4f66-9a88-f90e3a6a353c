"""
Pydantic models for API responses
"""
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime

class PriceData(BaseModel):
    """Price data response model"""
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float
    rsi: Optional[float] = None
    macd: Optional[float] = None
    macd_signal: Optional[float] = None
    bb_upper: Optional[float] = None
    bb_middle: Optional[float] = None
    bb_lower: Optional[float] = None

class TechnicalSignals(BaseModel):
    """Technical signals response model"""
    symbol: str
    timestamp: datetime
    price: float
    signals: Dict[str, str]

class SymbolInfo(BaseModel):
    """Symbol information model"""
    symbol: str
    current_price: float
    price_change_24h: Optional[float] = None
    volume_24h: Optional[float] = None
    last_update: datetime

class MarketOverview(BaseModel):
    """Market overview response model"""
    total_symbols: int
    symbols: List[SymbolInfo]
    last_update: datetime

class IndicatorData(BaseModel):
    """Technical indicator data model"""
    timestamp: datetime
    rsi: Optional[float] = None
    macd: Optional[float] = None
    macd_signal: Optional[float] = None
    bb_upper: Optional[float] = None
    bb_middle: Optional[float] = None
    bb_lower: Optional[float] = None

class ChartData(BaseModel):
    """Chart data response model"""
    symbol: str
    timeframe: str
    prices: List[PriceData]
    indicators: List[IndicatorData]

class SystemStatus(BaseModel):
    """System status response model"""
    status: str
    total_records: int
    symbols_count: int
    last_data_update: Optional[datetime] = None
    indicators_updated: bool
    uptime: str

class ErrorResponse(BaseModel):
    """Error response model"""
    error: str
    message: str
    timestamp: datetime
