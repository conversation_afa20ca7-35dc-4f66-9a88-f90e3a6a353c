"""
Pydantic models for API responses
"""
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime

class PriceData(BaseModel):
    """Price data response model"""
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float
    rsi: Optional[float] = None
    macd: Optional[float] = None
    macd_signal: Optional[float] = None
    bb_upper: Optional[float] = None
    bb_middle: Optional[float] = None
    bb_lower: Optional[float] = None

class TechnicalSignals(BaseModel):
    """Technical signals response model"""
    symbol: str
    timestamp: datetime
    price: float
    signals: Dict[str, str]

class SymbolInfo(BaseModel):
    """Symbol information model"""
    symbol: str
    current_price: float
    price_change_24h: Optional[float] = None
    volume_24h: Optional[float] = None
    last_update: datetime

class MarketOverview(BaseModel):
    """Market overview response model"""
    total_symbols: int
    symbols: List[SymbolInfo]
    last_update: datetime

class IndicatorData(BaseModel):
    """Technical indicator data model"""
    timestamp: datetime
    rsi: Optional[float] = None
    macd: Optional[float] = None
    macd_signal: Optional[float] = None
    bb_upper: Optional[float] = None
    bb_middle: Optional[float] = None
    bb_lower: Optional[float] = None

class ChartData(BaseModel):
    """Chart data response model"""
    symbol: str
    timeframe: str
    prices: List[PriceData]
    indicators: List[IndicatorData]

class SystemStatus(BaseModel):
    """System status response model"""
    status: str
    total_records: int
    symbols_count: int
    last_data_update: Optional[datetime] = None
    indicators_updated: bool
    uptime: str

class ErrorResponse(BaseModel):
    """Error response model"""
    error: str
    message: str
    timestamp: datetime

class PortfolioItem(BaseModel):
    """Portfolio item model"""
    symbol: str
    quantity: float
    avg_buy_price: float
    total_invested: float
    current_price: float
    current_value: float
    pnl: float
    pnl_percentage: float
    first_buy_date: datetime

class PortfolioSummary(BaseModel):
    """Portfolio summary model"""
    total_invested: float
    current_value: float
    total_pnl: float
    total_pnl_percentage: float
    positions: List[PortfolioItem]
    last_update: datetime

class AlertItem(BaseModel):
    """Alert item model"""
    id: int
    symbol: str
    alert_type: str
    target_value: float
    current_value: Optional[float] = None
    is_active: bool
    is_triggered: bool
    message: Optional[str] = None
    created_at: datetime

class AlertCreate(BaseModel):
    """Create alert model"""
    symbol: str
    alert_type: str
    target_value: float
    message: Optional[str] = None

class CandlestickData(BaseModel):
    """Candlestick data for TradingView-like charts"""
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float

class TechnicalIndicatorLine(BaseModel):
    """Technical indicator line data"""
    timestamp: datetime
    value: Optional[float] = None

class AdvancedChartData(BaseModel):
    """Advanced chart data with all indicators"""
    symbol: str
    timeframe: str
    candlesticks: List[CandlestickData]
    rsi: List[TechnicalIndicatorLine]
    macd: List[TechnicalIndicatorLine]
    macd_signal: List[TechnicalIndicatorLine]
    bb_upper: List[TechnicalIndicatorLine]
    bb_middle: List[TechnicalIndicatorLine]
    bb_lower: List[TechnicalIndicatorLine]
    volume: List[TechnicalIndicatorLine]
