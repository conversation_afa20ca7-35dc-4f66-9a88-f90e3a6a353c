"""
Database models for the crypto trading system
"""
from datetime import datetime
from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()

class CryptoPriceData(Base):
    """Model for storing cryptocurrency price data"""
    __tablename__ = "crypto_price_data"

    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String(20), nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)

    # OHLCV data
    open_price = Column(Float, nullable=False)
    high_price = Column(Float, nullable=False)
    low_price = Column(Float, nullable=False)
    close_price = Column(Float, nullable=False)
    volume = Column(Float, nullable=False)

    # Technical indicators (will be calculated)
    rsi = Column(Float, nullable=True)
    macd = Column(Float, nullable=True)
    macd_signal = Column(Float, nullable=True)
    bb_upper = Column(Float, nullable=True)
    bb_middle = Column(Float, nullable=True)
    bb_lower = Column(Float, nullable=True)

    # Exchange info
    exchange = Column(String(20), nullable=False, default="binance")

    created_at = Column(DateTime, default=datetime.utcnow)

    # Composite index for efficient queries
    __table_args__ = (
        Index('idx_symbol_timestamp', 'symbol', 'timestamp'),
        Index('idx_exchange_symbol', 'exchange', 'symbol'),
    )

class PredictionData(Base):
    """Model for storing AI predictions"""
    __tablename__ = "prediction_data"

    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String(20), nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)

    # Prediction details
    predicted_price = Column(Float, nullable=False)
    prediction_horizon = Column(Integer, nullable=False)  # minutes
    confidence_score = Column(Float, nullable=True)

    # Model info
    model_name = Column(String(50), nullable=False)
    model_version = Column(String(20), nullable=False)

    # Actual outcome (filled later)
    actual_price = Column(Float, nullable=True)
    accuracy = Column(Float, nullable=True)

    created_at = Column(DateTime, default=datetime.utcnow)

    __table_args__ = (
        Index('idx_symbol_timestamp_pred', 'symbol', 'timestamp'),
    )

class TradingSignal(Base):
    """Model for storing trading signals"""
    __tablename__ = "trading_signals"

    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String(20), nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)

    # Signal details
    signal_type = Column(String(10), nullable=False)  # BUY, SELL, HOLD
    strength = Column(Float, nullable=False)  # 0-1
    entry_price = Column(Float, nullable=True)
    stop_loss = Column(Float, nullable=True)
    take_profit = Column(Float, nullable=True)

    # Strategy info
    strategy_name = Column(String(50), nullable=False)
    reasoning = Column(Text, nullable=True)

    # Execution status
    executed = Column(Boolean, default=False)
    execution_price = Column(Float, nullable=True)
    execution_time = Column(DateTime, nullable=True)

    created_at = Column(DateTime, default=datetime.utcnow)

class SentimentData(Base):
    """Model for storing sentiment analysis data"""
    __tablename__ = "sentiment_data"

    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String(20), nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)

    # Sentiment scores
    sentiment_score = Column(Float, nullable=False)  # -1 to 1
    sentiment_label = Column(String(20), nullable=False)  # positive, negative, neutral

    # Source info
    source = Column(String(20), nullable=False)  # twitter, reddit, news
    text_content = Column(Text, nullable=True)
    source_url = Column(String(500), nullable=True)

    created_at = Column(DateTime, default=datetime.utcnow)

    __table_args__ = (
        Index('idx_symbol_timestamp_sent', 'symbol', 'timestamp'),
    )

class Portfolio(Base):
    """Model for storing portfolio data"""
    __tablename__ = "portfolio"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String(50), nullable=False, default="default")
    symbol = Column(String(20), nullable=False, index=True)

    # Position details
    quantity = Column(Float, nullable=False)
    avg_buy_price = Column(Float, nullable=False)
    total_invested = Column(Float, nullable=False)

    # Timestamps
    first_buy_date = Column(DateTime, nullable=False)
    last_update = Column(DateTime, default=datetime.utcnow)
    created_at = Column(DateTime, default=datetime.utcnow)

    __table_args__ = (
        Index('idx_user_symbol', 'user_id', 'symbol'),
    )

class PriceAlert(Base):
    """Model for storing price alerts"""
    __tablename__ = "price_alerts"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String(50), nullable=False, default="default")
    symbol = Column(String(20), nullable=False, index=True)

    # Alert details
    alert_type = Column(String(20), nullable=False)  # price_above, price_below, rsi_overbought, etc.
    target_value = Column(Float, nullable=False)
    current_value = Column(Float, nullable=True)

    # Status
    is_active = Column(Boolean, default=True)
    is_triggered = Column(Boolean, default=False)
    triggered_at = Column(DateTime, nullable=True)

    # Notification
    message = Column(Text, nullable=True)

    created_at = Column(DateTime, default=datetime.utcnow)

    __table_args__ = (
        Index('idx_user_active_alerts', 'user_id', 'is_active'),
    )
