"""
Technical analysis indicators for cryptocurrency data
"""
import pandas as pd
import numpy as np
from typing import Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class TechnicalIndicators:
    """Class for calculating technical analysis indicators"""
    
    @staticmethod
    def calculate_rsi(prices: pd.Series, period: int = 14) -> pd.Series:
        """
        Calculate Relative Strength Index (RSI)
        
        Args:
            prices: Series of closing prices
            period: Period for RSI calculation (default: 14)
            
        Returns:
            Series of RSI values
        """
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            
            return rsi
        except Exception as e:
            logger.error(f"Error calculating RSI: {e}")
            return pd.Series(index=prices.index, dtype=float)
    
    @staticmethod
    def calculate_macd(prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> <PERSON><PERSON>[pd.Series, pd.Series]:
        """
        Calculate MACD (Moving Average Convergence Divergence)
        
        Args:
            prices: Series of closing prices
            fast: Fast EMA period (default: 12)
            slow: Slow EMA period (default: 26)
            signal: Signal line EMA period (default: 9)
            
        Returns:
            Tuple of (MACD line, Signal line)
        """
        try:
            ema_fast = prices.ewm(span=fast).mean()
            ema_slow = prices.ewm(span=slow).mean()
            
            macd_line = ema_fast - ema_slow
            signal_line = macd_line.ewm(span=signal).mean()
            
            return macd_line, signal_line
        except Exception as e:
            logger.error(f"Error calculating MACD: {e}")
            return pd.Series(index=prices.index, dtype=float), pd.Series(index=prices.index, dtype=float)
    
    @staticmethod
    def calculate_bollinger_bands(prices: pd.Series, period: int = 20, std_dev: float = 2) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        Calculate Bollinger Bands
        
        Args:
            prices: Series of closing prices
            period: Period for moving average (default: 20)
            std_dev: Standard deviation multiplier (default: 2)
            
        Returns:
            Tuple of (Upper band, Middle band, Lower band)
        """
        try:
            middle_band = prices.rolling(window=period).mean()
            std = prices.rolling(window=period).std()
            
            upper_band = middle_band + (std * std_dev)
            lower_band = middle_band - (std * std_dev)
            
            return upper_band, middle_band, lower_band
        except Exception as e:
            logger.error(f"Error calculating Bollinger Bands: {e}")
            return (pd.Series(index=prices.index, dtype=float), 
                   pd.Series(index=prices.index, dtype=float), 
                   pd.Series(index=prices.index, dtype=float))
    
    @staticmethod
    def calculate_sma(prices: pd.Series, period: int) -> pd.Series:
        """
        Calculate Simple Moving Average (SMA)
        
        Args:
            prices: Series of closing prices
            period: Period for moving average
            
        Returns:
            Series of SMA values
        """
        try:
            return prices.rolling(window=period).mean()
        except Exception as e:
            logger.error(f"Error calculating SMA: {e}")
            return pd.Series(index=prices.index, dtype=float)
    
    @staticmethod
    def calculate_ema(prices: pd.Series, period: int) -> pd.Series:
        """
        Calculate Exponential Moving Average (EMA)
        
        Args:
            prices: Series of closing prices
            period: Period for moving average
            
        Returns:
            Series of EMA values
        """
        try:
            return prices.ewm(span=period).mean()
        except Exception as e:
            logger.error(f"Error calculating EMA: {e}")
            return pd.Series(index=prices.index, dtype=float)
    
    @staticmethod
    def calculate_stochastic(high: pd.Series, low: pd.Series, close: pd.Series, 
                           k_period: int = 14, d_period: int = 3) -> Tuple[pd.Series, pd.Series]:
        """
        Calculate Stochastic Oscillator
        
        Args:
            high: Series of high prices
            low: Series of low prices
            close: Series of closing prices
            k_period: Period for %K calculation (default: 14)
            d_period: Period for %D calculation (default: 3)
            
        Returns:
            Tuple of (%K, %D)
        """
        try:
            lowest_low = low.rolling(window=k_period).min()
            highest_high = high.rolling(window=k_period).max()
            
            k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
            d_percent = k_percent.rolling(window=d_period).mean()
            
            return k_percent, d_percent
        except Exception as e:
            logger.error(f"Error calculating Stochastic: {e}")
            return pd.Series(index=close.index, dtype=float), pd.Series(index=close.index, dtype=float)
    
    @staticmethod
    def calculate_atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """
        Calculate Average True Range (ATR)
        
        Args:
            high: Series of high prices
            low: Series of low prices
            close: Series of closing prices
            period: Period for ATR calculation (default: 14)
            
        Returns:
            Series of ATR values
        """
        try:
            prev_close = close.shift(1)
            
            tr1 = high - low
            tr2 = abs(high - prev_close)
            tr3 = abs(low - prev_close)
            
            true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            atr = true_range.rolling(window=period).mean()
            
            return atr
        except Exception as e:
            logger.error(f"Error calculating ATR: {e}")
            return pd.Series(index=close.index, dtype=float)
    
    @classmethod
    def calculate_all_indicators(cls, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate all technical indicators for a DataFrame
        
        Args:
            df: DataFrame with OHLCV data (columns: open, high, low, close, volume)
            
        Returns:
            DataFrame with all technical indicators added
        """
        try:
            result_df = df.copy()
            
            # RSI
            result_df['rsi'] = cls.calculate_rsi(df['close'])
            
            # MACD
            macd, macd_signal = cls.calculate_macd(df['close'])
            result_df['macd'] = macd
            result_df['macd_signal'] = macd_signal
            
            # Bollinger Bands
            bb_upper, bb_middle, bb_lower = cls.calculate_bollinger_bands(df['close'])
            result_df['bb_upper'] = bb_upper
            result_df['bb_middle'] = bb_middle
            result_df['bb_lower'] = bb_lower
            
            # Moving Averages
            result_df['sma_20'] = cls.calculate_sma(df['close'], 20)
            result_df['sma_50'] = cls.calculate_sma(df['close'], 50)
            result_df['ema_12'] = cls.calculate_ema(df['close'], 12)
            result_df['ema_26'] = cls.calculate_ema(df['close'], 26)
            
            # Stochastic
            stoch_k, stoch_d = cls.calculate_stochastic(df['high'], df['low'], df['close'])
            result_df['stoch_k'] = stoch_k
            result_df['stoch_d'] = stoch_d
            
            # ATR
            result_df['atr'] = cls.calculate_atr(df['high'], df['low'], df['close'])
            
            logger.info(f"Calculated all technical indicators for {len(df)} records")
            return result_df
            
        except Exception as e:
            logger.error(f"Error calculating all indicators: {e}")
            return df
