"""
Portfolio management system
"""
import logging
from typing import List, Dict, Optional
from datetime import datetime
from sqlalchemy.orm import Session
from src.database.database import get_db_session
from src.database.models import Portfolio, CryptoPriceData
from src.data_collection.binance_collector import BinanceCollector

logger = logging.getLogger(__name__)

class PortfolioManager:
    """Portfolio management class"""
    
    def __init__(self, user_id: str = "default"):
        self.user_id = user_id
        self.collector = BinanceCollector()
    
    def add_position(self, symbol: str, quantity: float, buy_price: float) -> bool:
        """Add or update a position in the portfolio"""
        try:
            db = get_db_session()
            
            # Check if position already exists
            existing = db.query(Portfolio).filter(
                Portfolio.user_id == self.user_id,
                Portfolio.symbol == symbol
            ).first()
            
            if existing:
                # Update existing position (average price calculation)
                total_quantity = existing.quantity + quantity
                total_invested = existing.total_invested + (quantity * buy_price)
                new_avg_price = total_invested / total_quantity
                
                existing.quantity = total_quantity
                existing.avg_buy_price = new_avg_price
                existing.total_invested = total_invested
                existing.last_update = datetime.utcnow()
            else:
                # Create new position
                position = Portfolio(
                    user_id=self.user_id,
                    symbol=symbol,
                    quantity=quantity,
                    avg_buy_price=buy_price,
                    total_invested=quantity * buy_price,
                    first_buy_date=datetime.utcnow()
                )
                db.add(position)
            
            db.commit()
            logger.info(f"Added position: {quantity} {symbol} at ${buy_price}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding position: {e}")
            db.rollback()
            return False
        finally:
            db.close()
    
    def remove_position(self, symbol: str, quantity: Optional[float] = None) -> bool:
        """Remove or reduce a position"""
        try:
            db = get_db_session()
            
            position = db.query(Portfolio).filter(
                Portfolio.user_id == self.user_id,
                Portfolio.symbol == symbol
            ).first()
            
            if not position:
                logger.warning(f"Position not found: {symbol}")
                return False
            
            if quantity is None or quantity >= position.quantity:
                # Remove entire position
                db.delete(position)
                logger.info(f"Removed entire position: {symbol}")
            else:
                # Reduce position
                position.quantity -= quantity
                position.total_invested = position.quantity * position.avg_buy_price
                position.last_update = datetime.utcnow()
                logger.info(f"Reduced position: {quantity} {symbol}")
            
            db.commit()
            return True
            
        except Exception as e:
            logger.error(f"Error removing position: {e}")
            db.rollback()
            return False
        finally:
            db.close()
    
    def get_portfolio_summary(self) -> Dict:
        """Get complete portfolio summary with current values"""
        try:
            db = get_db_session()
            
            positions = db.query(Portfolio).filter(
                Portfolio.user_id == self.user_id
            ).all()
            
            if not positions:
                return {
                    "total_invested": 0,
                    "current_value": 0,
                    "total_pnl": 0,
                    "total_pnl_percentage": 0,
                    "positions": [],
                    "last_update": datetime.utcnow()
                }
            
            portfolio_items = []
            total_invested = 0
            total_current_value = 0
            
            for position in positions:
                # Get current price
                current_price = self.collector.get_current_price(position.symbol)
                if not current_price:
                    logger.warning(f"Could not get current price for {position.symbol}")
                    current_price = position.avg_buy_price
                
                current_value = position.quantity * current_price
                pnl = current_value - position.total_invested
                pnl_percentage = (pnl / position.total_invested) * 100 if position.total_invested > 0 else 0
                
                portfolio_items.append({
                    "symbol": position.symbol,
                    "quantity": position.quantity,
                    "avg_buy_price": position.avg_buy_price,
                    "total_invested": position.total_invested,
                    "current_price": current_price,
                    "current_value": current_value,
                    "pnl": pnl,
                    "pnl_percentage": pnl_percentage,
                    "first_buy_date": position.first_buy_date
                })
                
                total_invested += position.total_invested
                total_current_value += current_value
            
            total_pnl = total_current_value - total_invested
            total_pnl_percentage = (total_pnl / total_invested) * 100 if total_invested > 0 else 0
            
            db.close()
            
            return {
                "total_invested": total_invested,
                "current_value": total_current_value,
                "total_pnl": total_pnl,
                "total_pnl_percentage": total_pnl_percentage,
                "positions": portfolio_items,
                "last_update": datetime.utcnow()
            }
            
        except Exception as e:
            logger.error(f"Error getting portfolio summary: {e}")
            return {
                "error": str(e),
                "total_invested": 0,
                "current_value": 0,
                "total_pnl": 0,
                "total_pnl_percentage": 0,
                "positions": [],
                "last_update": datetime.utcnow()
            }
    
    def get_position(self, symbol: str) -> Optional[Dict]:
        """Get specific position details"""
        try:
            db = get_db_session()
            
            position = db.query(Portfolio).filter(
                Portfolio.user_id == self.user_id,
                Portfolio.symbol == symbol
            ).first()
            
            if not position:
                return None
            
            current_price = self.collector.get_current_price(symbol)
            if not current_price:
                current_price = position.avg_buy_price
            
            current_value = position.quantity * current_price
            pnl = current_value - position.total_invested
            pnl_percentage = (pnl / position.total_invested) * 100 if position.total_invested > 0 else 0
            
            db.close()
            
            return {
                "symbol": position.symbol,
                "quantity": position.quantity,
                "avg_buy_price": position.avg_buy_price,
                "total_invested": position.total_invested,
                "current_price": current_price,
                "current_value": current_value,
                "pnl": pnl,
                "pnl_percentage": pnl_percentage,
                "first_buy_date": position.first_buy_date,
                "last_update": position.last_update
            }
            
        except Exception as e:
            logger.error(f"Error getting position {symbol}: {e}")
            return None
