"""
FastAPI application for crypto trading system
"""
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import logging
from pathlib import Path

from src.api.routes import router
from src.utils.logger import setup_logging

# Setup logging
setup_logging(log_level="INFO")
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Crypto Trading System API",
    description="AI-powered cryptocurrency trading system with technical analysis",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify allowed origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(router, prefix="/api/v1", tags=["API"])

# Create static directory if it doesn't exist
static_dir = Path("static")
static_dir.mkdir(exist_ok=True)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

@app.get("/", response_class=HTMLResponse)
async def dashboard():
    """Serve the main dashboard"""
    html_content = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Crypto Trading System Dashboard</title>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f5f5f5;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
            }
            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 20px;
                border-radius: 10px;
                margin-bottom: 20px;
                text-align: center;
            }
            .stats-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                margin-bottom: 30px;
            }
            .stat-card {
                background: white;
                padding: 20px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                text-align: center;
            }
            .stat-value {
                font-size: 2em;
                font-weight: bold;
                color: #667eea;
            }
            .stat-label {
                color: #666;
                margin-top: 5px;
            }
            .symbols-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
                margin-bottom: 30px;
            }
            .symbol-card {
                background: white;
                padding: 20px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .symbol-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 15px;
            }
            .symbol-name {
                font-size: 1.2em;
                font-weight: bold;
            }
            .price {
                font-size: 1.5em;
                font-weight: bold;
                color: #2ecc71;
            }
            .change {
                font-size: 0.9em;
                padding: 5px 10px;
                border-radius: 5px;
            }
            .positive { background-color: #d4edda; color: #155724; }
            .negative { background-color: #f8d7da; color: #721c24; }
            .signals {
                margin-top: 15px;
            }
            .signal {
                display: inline-block;
                padding: 5px 10px;
                margin: 2px;
                border-radius: 15px;
                font-size: 0.8em;
                font-weight: bold;
            }
            .signal.bullish { background-color: #d4edda; color: #155724; }
            .signal.bearish { background-color: #f8d7da; color: #721c24; }
            .signal.neutral { background-color: #e2e3e5; color: #383d41; }
            .signal.overbought { background-color: #fff3cd; color: #856404; }
            .signal.oversold { background-color: #cce7ff; color: #004085; }
            .loading {
                text-align: center;
                padding: 50px;
                color: #666;
            }
            .refresh-btn {
                background: #667eea;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
                margin: 10px;
            }
            .refresh-btn:hover {
                background: #5a6fd8;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🚀 Crypto Trading System Dashboard</h1>
                <p>AI-powered cryptocurrency analysis with technical indicators</p>
                <div style="margin-top: 10px;">
                    <button class="refresh-btn" onclick="loadData()">🔄 Refresh Data</button>
                    <a href="/static/advanced-dashboard.html" class="refresh-btn" style="text-decoration: none; margin-left: 10px;">📊 Advanced Charts</a>
                    <a href="/static/portfolio-dashboard.html" class="refresh-btn" style="text-decoration: none; margin-left: 10px;">💼 Portfolio</a>
                    <a href="/static/alerts-dashboard.html" class="refresh-btn" style="text-decoration: none; margin-left: 10px;">🚨 Alerts</a>
                </div>
            </div>

            <div class="stats-grid" id="stats">
                <div class="loading">Loading system statistics...</div>
            </div>

            <div class="symbols-grid" id="symbols">
                <div class="loading">Loading market data...</div>
            </div>
        </div>

        <script>
            async function loadSystemStatus() {
                try {
                    const response = await fetch('/api/v1/status');
                    const data = await response.json();

                    document.getElementById('stats').innerHTML = `
                        <div class="stat-card">
                            <div class="stat-value">${data.status}</div>
                            <div class="stat-label">System Status</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${data.total_records.toLocaleString()}</div>
                            <div class="stat-label">Total Records</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${data.symbols_count}</div>
                            <div class="stat-label">Symbols Tracked</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${data.indicators_updated ? '✅' : '❌'}</div>
                            <div class="stat-label">Indicators Updated</div>
                        </div>
                    `;
                } catch (error) {
                    console.error('Error loading system status:', error);
                }
            }

            async function loadMarketData() {
                try {
                    const response = await fetch('/api/v1/market');
                    const data = await response.json();

                    let symbolsHtml = '';

                    for (const symbol of data.symbols) {
                        const changeClass = symbol.price_change_24h > 0 ? 'positive' : 'negative';
                        const changeSign = symbol.price_change_24h > 0 ? '+' : '';

                        // Get signals for this symbol
                        let signalsHtml = '<div class="signals">Loading signals...</div>';

                        try {
                            const signalsResponse = await fetch(`/api/v1/signals/${symbol.symbol}`);
                            if (signalsResponse.ok) {
                                const signalsData = await signalsResponse.json();
                                signalsHtml = '<div class="signals">';

                                for (const [indicator, signal] of Object.entries(signalsData.signals)) {
                                    let signalClass = 'neutral';
                                    if (signal === 'BULLISH') signalClass = 'bullish';
                                    else if (signal === 'BEARISH') signalClass = 'bearish';
                                    else if (signal === 'OVERBOUGHT') signalClass = 'overbought';
                                    else if (signal === 'OVERSOLD') signalClass = 'oversold';

                                    signalsHtml += `<span class="signal ${signalClass}">${indicator.toUpperCase()}: ${signal}</span>`;
                                }
                                signalsHtml += '</div>';
                            }
                        } catch (e) {
                            signalsHtml = '<div class="signals">Signals unavailable</div>';
                        }

                        symbolsHtml += `
                            <div class="symbol-card">
                                <div class="symbol-header">
                                    <div class="symbol-name">${symbol.symbol}</div>
                                    <div class="price">$${symbol.current_price.toLocaleString()}</div>
                                </div>
                                <div class="change ${changeClass}">
                                    24h: ${changeSign}${symbol.price_change_24h ? symbol.price_change_24h.toFixed(2) : 'N/A'}%
                                </div>
                                ${signalsHtml}
                            </div>
                        `;
                    }

                    document.getElementById('symbols').innerHTML = symbolsHtml;
                } catch (error) {
                    console.error('Error loading market data:', error);
                    document.getElementById('symbols').innerHTML = '<div class="loading">Error loading market data</div>';
                }
            }

            async function loadData() {
                await loadSystemStatus();
                await loadMarketData();
            }

            // Load data on page load
            loadData();

            // Auto-refresh every 30 seconds
            setInterval(loadData, 30000);
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
