<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alert Management - Crypto Trading System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #0d1421;
            color: #ffffff;
            overflow-x: hidden;
        }

        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .header h1 {
            font-size: 1.8em;
            font-weight: 600;
        }

        .header-nav {
            display: flex;
            gap: 15px;
        }

        .nav-btn {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.2);
        }

        .main-container {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .alerts-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .summary-card {
            background: #131722;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .summary-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
        }

        .summary-card.active::before {
            background: linear-gradient(90deg, #00d4aa, #667eea);
        }

        .summary-card.triggered::before {
            background: linear-gradient(90deg, #ff4757, #ff6b6b);
        }

        .summary-card.inactive::before {
            background: linear-gradient(90deg, #888, #aaa);
        }

        .summary-value {
            font-size: 2.2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .summary-label {
            color: #888;
            font-size: 0.9em;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .alerts-section {
            background: #131722;
            border-radius: 10px;
            padding: 20px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .section-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #00d4aa;
        }

        .create-alert-btn {
            background: #00d4aa;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: background 0.3s;
        }

        .create-alert-btn:hover {
            background: #00b894;
        }

        .alerts-filters {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .filter-btn {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            color: white;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s;
        }

        .filter-btn.active {
            background: #00d4aa;
            border-color: #00d4aa;
        }

        .filter-btn:hover {
            background: rgba(255,255,255,0.2);
        }

        .alerts-table {
            width: 100%;
            border-collapse: collapse;
        }

        .alerts-table th,
        .alerts-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .alerts-table th {
            color: #888;
            font-weight: 500;
            font-size: 0.9em;
        }

        .alert-row {
            transition: background 0.3s;
        }

        .alert-row:hover {
            background: rgba(255,255,255,0.05);
        }

        .alert-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .status-active {
            background: rgba(0, 212, 170, 0.2);
            color: #00d4aa;
        }

        .status-triggered {
            background: rgba(255, 71, 87, 0.2);
            color: #ff4757;
        }

        .status-inactive {
            background: rgba(136, 136, 136, 0.2);
            color: #888;
        }

        .alert-type {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .type-price-above {
            background: rgba(0, 212, 170, 0.1);
            color: #00d4aa;
        }

        .type-price-below {
            background: rgba(255, 71, 87, 0.1);
            color: #ff4757;
        }

        .type-rsi-overbought {
            background: rgba(255, 165, 0, 0.1);
            color: #ffa502;
        }

        .type-rsi-oversold {
            background: rgba(116, 185, 255, 0.1);
            color: #74b9ff;
        }

        .action-btn {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8em;
            margin: 0 2px;
            transition: all 0.3s;
        }

        .action-btn:hover {
            background: rgba(255,255,255,0.2);
        }

        .action-btn.danger {
            background: rgba(255, 71, 87, 0.2);
            border-color: #ff4757;
        }

        .action-btn.danger:hover {
            background: rgba(255, 71, 87, 0.3);
        }

        .action-btn.toggle {
            background: rgba(255, 165, 0, 0.2);
            border-color: #ffa502;
        }

        .action-btn.toggle:hover {
            background: rgba(255, 165, 0, 0.3);
        }

        .quick-alerts {
            background: #131722;
            border-radius: 10px;
            padding: 20px;
        }

        .quick-alert-card {
            background: rgba(255,255,255,0.05);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s;
            border: 1px solid rgba(255,255,255,0.1);
        }

        .quick-alert-card:hover {
            background: rgba(255,255,255,0.1);
            transform: translateY(-2px);
        }

        .quick-alert-title {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .quick-alert-desc {
            font-size: 0.9em;
            color: #888;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
        }

        .modal-content {
            background: #131722;
            margin: 5% auto;
            padding: 30px;
            border-radius: 10px;
            width: 90%;
            max-width: 500px;
            position: relative;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            position: absolute;
            right: 15px;
            top: 15px;
        }

        .close:hover {
            color: white;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #888;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 6px;
            background: rgba(255,255,255,0.05);
            color: white;
            font-size: 14px;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #00d4aa;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .submit-btn {
            background: #00d4aa;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            width: 100%;
            font-size: 16px;
        }

        .submit-btn:hover {
            background: #00b894;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #888;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #888;
        }

        .empty-state-icon {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .triggered-alerts {
            background: rgba(255, 71, 87, 0.1);
            border: 1px solid rgba(255, 71, 87, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .triggered-alert-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 71, 87, 0.2);
        }

        .triggered-alert-item:last-child {
            border-bottom: none;
        }

        .alert-icon {
            font-size: 1.2em;
            margin-right: 10px;
        }

        @media (max-width: 1024px) {
            .content-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                gap: 10px;
            }

            .header-nav {
                flex-wrap: wrap;
                justify-content: center;
            }

            .main-container {
                padding: 10px;
            }

            .alerts-table {
                font-size: 0.9em;
            }

            .alerts-table th,
            .alerts-table td {
                padding: 8px 4px;
            }

            .alerts-filters {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚨 Alert Management</h1>
        <div class="header-nav">
            <a href="/" class="nav-btn">🏠 Dashboard</a>
            <a href="/static/advanced-dashboard.html" class="nav-btn">📊 Charts</a>
            <a href="/static/portfolio-dashboard.html" class="nav-btn">💼 Portfolio</a>
            <a href="/docs" class="nav-btn">📖 API</a>
            <button class="nav-btn" onclick="refreshData()">🔄 Refresh</button>
        </div>
    </div>

    <div class="main-container">
        <!-- Alerts Summary -->
        <div class="alerts-summary" id="alertsSummary">
            <div class="loading">Loading alerts summary...</div>
        </div>

        <!-- Triggered Alerts Section -->
        <div id="triggeredAlertsSection" style="display: none;">
            <div class="triggered-alerts">
                <h3 style="margin-bottom: 15px; color: #ff4757;">🚨 Recently Triggered Alerts</h3>
                <div id="triggeredAlertsList"></div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="content-grid">
            <!-- Alerts Table -->
            <div class="alerts-section">
                <div class="section-header">
                    <div class="section-title">Active Alerts</div>
                    <button class="create-alert-btn" onclick="openCreateAlertModal()">+ Create Alert</button>
                </div>

                <!-- Filters -->
                <div class="alerts-filters">
                    <button class="filter-btn active" data-filter="all" onclick="filterAlerts('all')">All</button>
                    <button class="filter-btn" data-filter="active" onclick="filterAlerts('active')">Active</button>
                    <button class="filter-btn" data-filter="triggered" onclick="filterAlerts('triggered')">Triggered</button>
                    <button class="filter-btn" data-filter="inactive" onclick="filterAlerts('inactive')">Inactive</button>
                </div>

                <div id="alertsContainer">
                    <div class="loading">Loading alerts...</div>
                </div>
            </div>

            <!-- Quick Alerts Section -->
            <div class="quick-alerts">
                <div class="section-header">
                    <div class="section-title">Quick Alerts</div>
                </div>

                <div class="quick-alert-card" onclick="createQuickAlert('price_above')">
                    <div class="quick-alert-title">📈 Price Above</div>
                    <div class="quick-alert-desc">Alert when price goes above target</div>
                </div>

                <div class="quick-alert-card" onclick="createQuickAlert('price_below')">
                    <div class="quick-alert-title">📉 Price Below</div>
                    <div class="quick-alert-desc">Alert when price goes below target</div>
                </div>

                <div class="quick-alert-card" onclick="createQuickAlert('rsi_overbought')">
                    <div class="quick-alert-title">⚠️ RSI Overbought</div>
                    <div class="quick-alert-desc">Alert when RSI indicates overbought</div>
                </div>

                <div class="quick-alert-card" onclick="createQuickAlert('rsi_oversold')">
                    <div class="quick-alert-title">💎 RSI Oversold</div>
                    <div class="quick-alert-desc">Alert when RSI indicates oversold</div>
                </div>

                <div class="quick-alert-card" onclick="checkAllAlerts()">
                    <div class="quick-alert-title">🔍 Check All Alerts</div>
                    <div class="quick-alert-desc">Manually trigger alert checking</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Alert Modal -->
    <div id="createAlertModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeCreateAlertModal()">&times;</span>
            <h2 style="margin-bottom: 20px; color: #00d4aa;">Create New Alert</h2>
            <form id="createAlertForm">
                <div class="form-group">
                    <label for="alertSymbol">Symbol</label>
                    <select id="alertSymbol" required>
                        <option value="">Select Symbol</option>
                        <option value="BTCUSDT">BTC/USDT</option>
                        <option value="ETHUSDT">ETH/USDT</option>
                        <option value="BNBUSDT">BNB/USDT</option>
                        <option value="ADAUSDT">ADA/USDT</option>
                        <option value="XRPUSDT">XRP/USDT</option>
                        <option value="SOLUSDT">SOL/USDT</option>
                        <option value="DOTUSDT">DOT/USDT</option>
                        <option value="DOGEUSDT">DOGE/USDT</option>
                        <option value="AVAXUSDT">AVAX/USDT</option>
                        <option value="LINKUSDT">LINK/USDT</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="alertType">Alert Type</label>
                    <select id="alertType" required onchange="updateTargetValueLabel()">
                        <option value="">Select Alert Type</option>
                        <option value="price_above">Price Above</option>
                        <option value="price_below">Price Below</option>
                        <option value="rsi_overbought">RSI Overbought</option>
                        <option value="rsi_oversold">RSI Oversold</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="targetValue" id="targetValueLabel">Target Value</label>
                    <input type="number" id="targetValue" step="0.01" required placeholder="0.00">
                </div>
                <div class="form-group">
                    <label for="alertMessage">Message (Optional)</label>
                    <textarea id="alertMessage" placeholder="Custom alert message..."></textarea>
                </div>
                <button type="submit" class="submit-btn">Create Alert</button>
            </form>
        </div>
    </div>

    <script>
        let alertsData = [];
        let currentFilter = 'all';

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadAlertsData();

            // Setup form submission
            document.getElementById('createAlertForm').addEventListener('submit', handleCreateAlert);

            // Auto-refresh every 30 seconds
            setInterval(loadAlertsData, 30000);
        });

        async function loadAlertsData() {
            try {
                const response = await fetch('/api/v1/alerts');
                const data = await response.json();
                alertsData = data.alerts || [];

                renderAlertsSummary();
                renderAlertsTable();
                checkForTriggeredAlerts();

            } catch (error) {
                console.error('Error loading alerts data:', error);
                showError('Failed to load alerts data');
            }
        }

        function renderAlertsSummary() {
            const activeAlerts = alertsData.filter(alert => alert.is_active && !alert.is_triggered).length;
            const triggeredAlerts = alertsData.filter(alert => alert.is_triggered).length;
            const inactiveAlerts = alertsData.filter(alert => !alert.is_active).length;
            const totalAlerts = alertsData.length;

            const summaryHtml = `
                <div class="summary-card active">
                    <div class="summary-value">${activeAlerts}</div>
                    <div class="summary-label">Active Alerts</div>
                </div>
                <div class="summary-card triggered">
                    <div class="summary-value">${triggeredAlerts}</div>
                    <div class="summary-label">Triggered</div>
                </div>
                <div class="summary-card inactive">
                    <div class="summary-value">${inactiveAlerts}</div>
                    <div class="summary-label">Inactive</div>
                </div>
                <div class="summary-card">
                    <div class="summary-value">${totalAlerts}</div>
                    <div class="summary-label">Total Alerts</div>
                </div>
            `;

            document.getElementById('alertsSummary').innerHTML = summaryHtml;
        }

        function renderAlertsTable() {
            const filteredAlerts = filterAlertsByType(alertsData, currentFilter);

            if (filteredAlerts.length === 0) {
                document.getElementById('alertsContainer').innerHTML = `
                    <div class="empty-state">
                        <div class="empty-state-icon">🚨</div>
                        <h3>No Alerts Found</h3>
                        <p>Create your first alert to get notified about price movements</p>
                        <button class="create-alert-btn" onclick="openCreateAlertModal()" style="margin-top: 20px;">+ Create Alert</button>
                    </div>
                `;
                return;
            }

            let tableHtml = `
                <table class="alerts-table">
                    <thead>
                        <tr>
                            <th>Symbol</th>
                            <th>Type</th>
                            <th>Target</th>
                            <th>Current</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            filteredAlerts.forEach(alert => {
                const statusClass = alert.is_triggered ? 'status-triggered' :
                                  alert.is_active ? 'status-active' : 'status-inactive';
                const statusText = alert.is_triggered ? 'Triggered' :
                                 alert.is_active ? 'Active' : 'Inactive';

                const typeClass = `type-${alert.alert_type.replace('_', '-')}`;
                const typeText = alert.alert_type.replace('_', ' ').toUpperCase();

                const createdDate = new Date(alert.created_at).toLocaleDateString();
                const currentValue = alert.current_value ?
                    (alert.alert_type.includes('price') ? `$${alert.current_value.toLocaleString()}` : alert.current_value.toFixed(2))
                    : 'N/A';
                const targetValue = alert.alert_type.includes('price') ?
                    `$${alert.target_value.toLocaleString()}` : alert.target_value;

                tableHtml += `
                    <tr class="alert-row">
                        <td><strong>${alert.symbol}</strong></td>
                        <td><span class="alert-type ${typeClass}">${typeText}</span></td>
                        <td>${targetValue}</td>
                        <td>${currentValue}</td>
                        <td><span class="alert-status ${statusClass}">${statusText}</span></td>
                        <td>${createdDate}</td>
                        <td>
                            <button class="action-btn toggle" onclick="toggleAlert(${alert.id})" title="Toggle Active/Inactive">
                                ${alert.is_active ? '⏸️' : '▶️'}
                            </button>
                            <button class="action-btn danger" onclick="deleteAlert(${alert.id})" title="Delete Alert">🗑️</button>
                        </td>
                    </tr>
                `;
            });

            tableHtml += `
                    </tbody>
                </table>
            `;

            document.getElementById('alertsContainer').innerHTML = tableHtml;
        }

        function filterAlertsByType(alerts, filter) {
            switch (filter) {
                case 'active':
                    return alerts.filter(alert => alert.is_active && !alert.is_triggered);
                case 'triggered':
                    return alerts.filter(alert => alert.is_triggered);
                case 'inactive':
                    return alerts.filter(alert => !alert.is_active);
                default:
                    return alerts;
            }
        }

        function filterAlerts(filter) {
            currentFilter = filter;

            // Update filter buttons
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-filter="${filter}"]`).classList.add('active');

            // Update section title based on filter
            const sectionTitle = document.querySelector('.alerts-section .section-title');
            switch(filter) {
                case 'active':
                    sectionTitle.textContent = 'Active Alerts';
                    break;
                case 'triggered':
                    sectionTitle.textContent = 'Triggered Alerts';
                    break;
                case 'inactive':
                    sectionTitle.textContent = 'Inactive Alerts';
                    break;
                default:
                    sectionTitle.textContent = 'All Alerts';
            }

            renderAlertsTable();
        }

        function checkForTriggeredAlerts() {
            const recentlyTriggered = alertsData.filter(alert =>
                alert.is_triggered && alert.triggered_at &&
                new Date(alert.triggered_at) > new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
            );

            if (recentlyTriggered.length > 0) {
                let triggeredHtml = '';
                recentlyTriggered.forEach(alert => {
                    const triggeredTime = new Date(alert.triggered_at).toLocaleString();
                    triggeredHtml += `
                        <div class="triggered-alert-item">
                            <div>
                                <span class="alert-icon">🚨</span>
                                <strong>${alert.symbol}</strong> - ${alert.alert_type.replace('_', ' ').toUpperCase()}
                                <div style="font-size: 0.8em; color: #888; margin-top: 5px;">
                                    ${alert.message || 'Alert triggered'} - ${triggeredTime}
                                </div>
                            </div>
                            <button class="action-btn danger" onclick="dismissTriggeredAlert(${alert.id})">Dismiss</button>
                        </div>
                    `;
                });

                document.getElementById('triggeredAlertsList').innerHTML = triggeredHtml;
                document.getElementById('triggeredAlertsSection').style.display = 'block';
            } else {
                document.getElementById('triggeredAlertsSection').style.display = 'none';
            }
        }

        // Modal functions
        function openCreateAlertModal() {
            document.getElementById('createAlertModal').style.display = 'block';
        }

        function closeCreateAlertModal() {
            document.getElementById('createAlertModal').style.display = 'none';
            document.getElementById('createAlertForm').reset();

            // Reset target value label to default
            document.getElementById('targetValueLabel').textContent = 'Target Value';
            const input = document.getElementById('targetValue');
            input.placeholder = '0.00';
            input.min = '0';
            input.max = '1000000';
            input.step = '0.01';
        }

        function updateTargetValueLabel() {
            const alertType = document.getElementById('alertType').value;
            const label = document.getElementById('targetValueLabel');
            const input = document.getElementById('targetValue');

            if (alertType.includes('price')) {
                label.textContent = 'Target Price (USDT)';
                input.placeholder = '0.00';
                input.min = '0.01';
                input.max = '1000000';
                input.step = '0.01';
            } else if (alertType.includes('rsi')) {
                label.textContent = 'RSI Threshold (0-100)';
                input.placeholder = alertType.includes('overbought') ? '70' : '30';
                input.min = '0';
                input.max = '100';
                input.step = '1';
            } else {
                label.textContent = 'Target Value';
                input.placeholder = '0.00';
                input.min = '0';
                input.max = '1000000';
                input.step = '0.01';
            }
        }

        // Form submission
        async function handleCreateAlert(event) {
            event.preventDefault();

            const symbol = document.getElementById('alertSymbol').value;
            const alertType = document.getElementById('alertType').value;
            const targetValue = parseFloat(document.getElementById('targetValue').value);
            const message = document.getElementById('alertMessage').value;

            // Validation
            if (!symbol) {
                showError('Please select a symbol');
                return;
            }

            if (!alertType) {
                showError('Please select an alert type');
                return;
            }

            if (!targetValue || targetValue <= 0) {
                showError('Please enter a valid target value');
                return;
            }

            // Additional validation based on alert type
            if (alertType.includes('rsi') && (targetValue < 0 || targetValue > 100)) {
                showError('RSI value must be between 0 and 100');
                return;
            }

            if (alertType.includes('price') && targetValue < 0.01) {
                showError('Price must be at least $0.01');
                return;
            }

            try {
                const response = await fetch('/api/v1/alerts/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        symbol: symbol,
                        alert_type: alertType,
                        target_value: targetValue,
                        message: message || `${alertType.replace('_', ' ')} alert for ${symbol}`
                    })
                });

                if (response.ok) {
                    closeCreateAlertModal();
                    await loadAlertsData();
                    showSuccess('Alert created successfully!');
                } else {
                    const error = await response.json();
                    showError(error.detail || 'Failed to create alert');
                }
            } catch (error) {
                console.error('Error creating alert:', error);
                showError('Failed to create alert');
            }
        }

        // Quick alert functions
        function createQuickAlert(alertType) {
            // Reset form first
            document.getElementById('createAlertForm').reset();

            // Set alert type
            document.getElementById('alertType').value = alertType;
            updateTargetValueLabel();

            // Set default values based on alert type
            const targetInput = document.getElementById('targetValue');
            if (alertType === 'rsi_overbought') {
                targetInput.value = '70';
            } else if (alertType === 'rsi_oversold') {
                targetInput.value = '30';
            }

            openCreateAlertModal();

            // Focus on symbol selection
            setTimeout(() => {
                document.getElementById('alertSymbol').focus();
            }, 100);
        }

        // Alert management functions
        async function toggleAlert(alertId) {
            try {
                const response = await fetch(`/api/v1/alerts/${alertId}/toggle`, {
                    method: 'POST'
                });

                if (response.ok) {
                    await loadAlertsData();
                    // Re-apply current filter after data reload
                    renderAlertsTable();
                    showSuccess('Alert status updated!');
                } else {
                    const error = await response.json();
                    showError(error.detail || 'Failed to toggle alert');
                }
            } catch (error) {
                console.error('Error toggling alert:', error);
                showError('Failed to toggle alert');
            }
        }

        async function deleteAlert(alertId) {
            if (!confirm('Are you sure you want to delete this alert?')) {
                return;
            }

            try {
                const response = await fetch(`/api/v1/alerts/${alertId}`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    await loadAlertsData();
                    // Re-apply current filter after data reload
                    renderAlertsTable();
                    showSuccess('Alert deleted successfully!');
                } else {
                    const error = await response.json();
                    showError(error.detail || 'Failed to delete alert');
                }
            } catch (error) {
                console.error('Error deleting alert:', error);
                showError('Failed to delete alert');
            }
        }

        async function checkAllAlerts() {
            try {
                showSuccess('Checking all alerts...');
                const response = await fetch('/api/v1/alerts/check', {
                    method: 'POST'
                });

                if (response.ok) {
                    const data = await response.json();
                    await loadAlertsData();

                    if (data.count > 0) {
                        showSuccess(`${data.count} alert(s) triggered!`);
                    } else {
                        showSuccess('No alerts triggered');
                    }
                } else {
                    const error = await response.json();
                    showError(error.detail || 'Failed to check alerts');
                }
            } catch (error) {
                console.error('Error checking alerts:', error);
                showError('Failed to check alerts');
            }
        }

        async function dismissTriggeredAlert(alertId) {
            // For now, just hide the triggered alert section
            // In a real app, you might want to mark it as "acknowledged"
            document.getElementById('triggeredAlertsSection').style.display = 'none';
        }

        async function refreshData() {
            await loadAlertsData();
            showSuccess('Data refreshed!');
        }

        // Utility functions
        function showSuccess(message) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #00d4aa;
                color: white;
                padding: 15px 20px;
                border-radius: 6px;
                z-index: 10000;
                font-weight: 500;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                document.body.removeChild(notification);
            }, 3000);
        }

        function showError(message) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #ff4757;
                color: white;
                padding: 15px 20px;
                border-radius: 6px;
                z-index: 10000;
                font-weight: 500;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                document.body.removeChild(notification);
            }, 3000);
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('createAlertModal');
            if (event.target == modal) {
                closeCreateAlertModal();
            }
        }
    </script>
</body>
</html>
