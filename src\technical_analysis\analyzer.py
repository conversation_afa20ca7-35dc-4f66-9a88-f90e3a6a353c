"""
Technical analysis processor for updating database with indicators
"""
import pandas as pd
import logging
from typing import List, Optional
from sqlalchemy.orm import Session
from src.database.database import get_db_session
from src.database.models import CryptoPriceData
from src.technical_analysis.indicators import TechnicalIndicators
from config.settings import settings

logger = logging.getLogger(__name__)

class TechnicalAnalyzer:
    """Class for processing technical analysis on stored data"""
    
    def __init__(self):
        self.indicators = TechnicalIndicators()
    
    def get_price_data(self, symbol: str, limit: Optional[int] = None) -> pd.DataFrame:
        """
        Get price data from database for a symbol
        
        Args:
            symbol: Trading pair symbol (e.g., 'BTCUSDT')
            limit: Maximum number of records to retrieve
            
        Returns:
            DataFrame with OHLCV data
        """
        try:
            db = get_db_session()
            
            query = db.query(CryptoPriceData).filter(
                CryptoPriceData.symbol == symbol
            ).order_by(CryptoPriceData.timestamp)
            
            if limit:
                query = query.limit(limit)
            
            records = query.all()
            
            if not records:
                logger.warning(f"No data found for symbol {symbol}")
                return pd.DataFrame()
            
            # Convert to DataFrame
            data = []
            for record in records:
                data.append({
                    'id': record.id,
                    'timestamp': record.timestamp,
                    'open': record.open_price,
                    'high': record.high_price,
                    'low': record.low_price,
                    'close': record.close_price,
                    'volume': record.volume,
                    'rsi': record.rsi,
                    'macd': record.macd,
                    'macd_signal': record.macd_signal,
                    'bb_upper': record.bb_upper,
                    'bb_middle': record.bb_middle,
                    'bb_lower': record.bb_lower
                })
            
            df = pd.DataFrame(data)
            df.set_index('timestamp', inplace=True)
            
            logger.info(f"Retrieved {len(df)} records for {symbol}")
            return df
            
        except Exception as e:
            logger.error(f"Error getting price data for {symbol}: {e}")
            return pd.DataFrame()
        finally:
            db.close()
    
    def update_indicators_for_symbol(self, symbol: str) -> bool:
        """
        Calculate and update technical indicators for a symbol
        
        Args:
            symbol: Trading pair symbol
            
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(f"Updating technical indicators for {symbol}")
            
            # Get price data
            df = self.get_price_data(symbol)
            if df.empty:
                logger.warning(f"No data available for {symbol}")
                return False
            
            # Calculate indicators
            df_with_indicators = self.indicators.calculate_all_indicators(df)
            
            # Update database
            db = get_db_session()
            updated_count = 0
            
            for timestamp, row in df_with_indicators.iterrows():
                # Find the record to update
                record = db.query(CryptoPriceData).filter(
                    CryptoPriceData.symbol == symbol,
                    CryptoPriceData.timestamp == timestamp
                ).first()
                
                if record:
                    # Update technical indicators
                    record.rsi = row.get('rsi') if pd.notna(row.get('rsi')) else None
                    record.macd = row.get('macd') if pd.notna(row.get('macd')) else None
                    record.macd_signal = row.get('macd_signal') if pd.notna(row.get('macd_signal')) else None
                    record.bb_upper = row.get('bb_upper') if pd.notna(row.get('bb_upper')) else None
                    record.bb_middle = row.get('bb_middle') if pd.notna(row.get('bb_middle')) else None
                    record.bb_lower = row.get('bb_lower') if pd.notna(row.get('bb_lower')) else None
                    
                    updated_count += 1
            
            db.commit()
            logger.info(f"Updated {updated_count} records with technical indicators for {symbol}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating indicators for {symbol}: {e}")
            db.rollback()
            return False
        finally:
            db.close()
    
    def update_all_symbols(self, symbols: List[str] = None) -> dict:
        """
        Update technical indicators for all symbols
        
        Args:
            symbols: List of symbols to update (default: all supported symbols)
            
        Returns:
            Dictionary with results for each symbol
        """
        if symbols is None:
            symbols = settings.supported_symbols
        
        results = {}
        
        for symbol in symbols:
            try:
                success = self.update_indicators_for_symbol(symbol)
                results[symbol] = "success" if success else "failed"
                logger.info(f"Technical analysis for {symbol}: {'✅' if success else '❌'}")
                
            except Exception as e:
                logger.error(f"Error processing {symbol}: {e}")
                results[symbol] = f"error: {str(e)}"
        
        return results
    
    def get_latest_indicators(self, symbol: str, limit: int = 100) -> pd.DataFrame:
        """
        Get latest technical indicators for a symbol
        
        Args:
            symbol: Trading pair symbol
            limit: Number of latest records to retrieve
            
        Returns:
            DataFrame with latest indicators
        """
        try:
            db = get_db_session()
            
            records = db.query(CryptoPriceData).filter(
                CryptoPriceData.symbol == symbol
            ).order_by(CryptoPriceData.timestamp.desc()).limit(limit).all()
            
            if not records:
                return pd.DataFrame()
            
            data = []
            for record in records:
                data.append({
                    'timestamp': record.timestamp,
                    'close': record.close_price,
                    'rsi': record.rsi,
                    'macd': record.macd,
                    'macd_signal': record.macd_signal,
                    'bb_upper': record.bb_upper,
                    'bb_middle': record.bb_middle,
                    'bb_lower': record.bb_lower
                })
            
            df = pd.DataFrame(data)
            df.set_index('timestamp', inplace=True)
            df.sort_index(inplace=True)  # Sort by timestamp ascending
            
            return df
            
        except Exception as e:
            logger.error(f"Error getting latest indicators for {symbol}: {e}")
            return pd.DataFrame()
        finally:
            db.close()
    
    def analyze_signals(self, symbol: str) -> dict:
        """
        Analyze trading signals based on technical indicators
        
        Args:
            symbol: Trading pair symbol
            
        Returns:
            Dictionary with signal analysis
        """
        try:
            df = self.get_latest_indicators(symbol, limit=50)
            if df.empty:
                return {"error": "No data available"}
            
            latest = df.iloc[-1]
            signals = {
                "symbol": symbol,
                "timestamp": latest.name,
                "price": latest['close'],
                "signals": {}
            }
            
            # RSI signals
            if pd.notna(latest['rsi']):
                if latest['rsi'] > 70:
                    signals["signals"]["rsi"] = "OVERBOUGHT"
                elif latest['rsi'] < 30:
                    signals["signals"]["rsi"] = "OVERSOLD"
                else:
                    signals["signals"]["rsi"] = "NEUTRAL"
            
            # MACD signals
            if pd.notna(latest['macd']) and pd.notna(latest['macd_signal']):
                if latest['macd'] > latest['macd_signal']:
                    signals["signals"]["macd"] = "BULLISH"
                else:
                    signals["signals"]["macd"] = "BEARISH"
            
            # Bollinger Bands signals
            if all(pd.notna(latest[col]) for col in ['bb_upper', 'bb_middle', 'bb_lower']):
                price = latest['close']
                if price > latest['bb_upper']:
                    signals["signals"]["bollinger"] = "OVERBOUGHT"
                elif price < latest['bb_lower']:
                    signals["signals"]["bollinger"] = "OVERSOLD"
                else:
                    signals["signals"]["bollinger"] = "NEUTRAL"
            
            return signals
            
        except Exception as e:
            logger.error(f"Error analyzing signals for {symbol}: {e}")
            return {"error": str(e)}
