"""
Test script for technical analysis module
"""
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.utils.logger import setup_logging
from src.technical_analysis.analyzer import TechnicalAnalyzer
from src.technical_analysis.indicators import TechnicalIndicators
import pandas as pd

def test_indicators():
    """Test technical indicators calculation"""
    print("🧪 Testing technical indicators...")
    
    try:
        # Create sample data
        dates = pd.date_range('2024-01-01', periods=100, freq='1min')
        np_random = __import__('numpy').random
        
        # Generate sample OHLCV data
        base_price = 50000
        data = {
            'open': [base_price + np_random.randint(-1000, 1000) for _ in range(100)],
            'high': [base_price + np_random.randint(-500, 1500) for _ in range(100)],
            'low': [base_price + np_random.randint(-1500, 500) for _ in range(100)],
            'close': [base_price + np_random.randint(-1000, 1000) for _ in range(100)],
            'volume': [np_random.randint(100, 1000) for _ in range(100)]
        }
        
        df = pd.DataFrame(data, index=dates)
        
        # Test individual indicators
        indicators = TechnicalIndicators()
        
        # Test RSI
        rsi = indicators.calculate_rsi(df['close'])
        print(f"✅ RSI calculated: {len(rsi.dropna())} values")
        
        # Test MACD
        macd, signal = indicators.calculate_macd(df['close'])
        print(f"✅ MACD calculated: {len(macd.dropna())} values")
        
        # Test Bollinger Bands
        bb_upper, bb_middle, bb_lower = indicators.calculate_bollinger_bands(df['close'])
        print(f"✅ Bollinger Bands calculated: {len(bb_middle.dropna())} values")
        
        # Test all indicators
        df_with_indicators = indicators.calculate_all_indicators(df)
        print(f"✅ All indicators calculated. Columns: {len(df_with_indicators.columns)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing indicators: {e}")
        return False

def test_analyzer():
    """Test technical analyzer with real data"""
    print("\n🧪 Testing technical analyzer...")
    
    try:
        analyzer = TechnicalAnalyzer()
        
        # Test getting data for BTC
        df = analyzer.get_price_data("BTCUSDT", limit=1000)
        if not df.empty:
            print(f"✅ Retrieved {len(df)} records for BTCUSDT")
        else:
            print("⚠️  No data found for BTCUSDT")
            return False
        
        # Test updating indicators
        success = analyzer.update_indicators_for_symbol("BTCUSDT")
        if success:
            print("✅ Technical indicators updated successfully")
        else:
            print("❌ Failed to update technical indicators")
            return False
        
        # Test getting latest indicators
        latest_df = analyzer.get_latest_indicators("BTCUSDT", limit=10)
        if not latest_df.empty:
            print(f"✅ Retrieved latest indicators: {len(latest_df)} records")
            print(f"   Latest RSI: {latest_df['rsi'].iloc[-1]:.2f}" if pd.notna(latest_df['rsi'].iloc[-1]) else "   Latest RSI: N/A")
        
        # Test signal analysis
        signals = analyzer.analyze_signals("BTCUSDT")
        if "error" not in signals:
            print("✅ Signal analysis completed")
            print(f"   Current price: ${signals.get('price', 0):,.2f}")
            print(f"   Signals: {signals.get('signals', {})}")
        else:
            print(f"❌ Signal analysis failed: {signals['error']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing analyzer: {e}")
        return False

def test_multiple_symbols():
    """Test updating indicators for multiple symbols"""
    print("\n🧪 Testing multiple symbols...")
    
    try:
        analyzer = TechnicalAnalyzer()
        
        # Test with first 3 symbols
        test_symbols = ["BTCUSDT", "ETHUSDT", "BNBUSDT"]
        results = analyzer.update_all_symbols(test_symbols)
        
        print("✅ Multiple symbols processing completed:")
        for symbol, result in results.items():
            status = "✅" if result == "success" else "❌"
            print(f"   {symbol}: {status} {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing multiple symbols: {e}")
        return False

def main():
    """Run all technical analysis tests"""
    print("🚀 Technical Analysis Test Suite")
    print("=" * 50)
    
    # Setup logging
    setup_logging(log_level="INFO")
    
    tests = [
        test_indicators,
        test_analyzer,
        test_multiple_symbols
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All technical analysis tests passed!")
    else:
        print("⚠️  Some tests failed. Check the errors above.")

if __name__ == "__main__":
    main()
