"""
Update technical indicators for all symbols in the database
"""
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.utils.logger import setup_logging
from src.technical_analysis.analyzer import TechnicalAnalyzer
from config.settings import settings

def main():
    """Update technical indicators for all symbols"""
    # Setup logging
    logger = setup_logging(log_level="INFO")
    logger.info("🚀 Starting technical indicators update for all symbols...")
    
    try:
        analyzer = TechnicalAnalyzer()
        
        # Update all supported symbols
        logger.info(f"Updating indicators for {len(settings.supported_symbols)} symbols...")
        results = analyzer.update_all_symbols()
        
        # Show results
        logger.info("📊 Technical Analysis Results:")
        successful = 0
        failed = 0
        
        for symbol, result in results.items():
            if result == "success":
                successful += 1
                logger.info(f"   ✅ {symbol}: SUCCESS")
            else:
                failed += 1
                logger.error(f"   ❌ {symbol}: {result}")
        
        logger.info(f"📈 Summary: {successful} successful, {failed} failed")
        
        if successful > 0:
            logger.info("🎉 Technical indicators update completed!")
            
            # Show sample analysis for BTC
            logger.info("📊 Sample analysis for BTCUSDT:")
            signals = analyzer.analyze_signals("BTCUSDT")
            if "error" not in signals:
                logger.info(f"   Price: ${signals.get('price', 0):,.2f}")
                logger.info(f"   RSI: {signals['signals'].get('rsi', 'N/A')}")
                logger.info(f"   MACD: {signals['signals'].get('macd', 'N/A')}")
                logger.info(f"   Bollinger: {signals['signals'].get('bollinger', 'N/A')}")
        
    except Exception as e:
        logger.error(f"❌ Error updating indicators: {e}")
        raise

if __name__ == "__main__":
    main()
