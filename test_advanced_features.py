"""
Test script for advanced features: Portfolio and Alerts
"""
import requests
import json
import time

def test_portfolio_features():
    """Test portfolio management features"""
    base_url = "http://localhost:8000/api/v1"
    
    print("🧪 Testing Portfolio Management")
    print("=" * 50)
    
    # Test 1: Get empty portfolio
    print("1. Testing empty portfolio...")
    try:
        response = requests.get(f"{base_url}/portfolio")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Empty portfolio: ${data.get('total_invested', 0):,.2f} invested")
        else:
            print(f"   ❌ Portfolio check failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Portfolio check error: {e}")
    
    # Test 2: Add positions
    print("\n2. Adding positions...")
    positions = [
        {"symbol": "BTCUSDT", "quantity": 0.1, "buy_price": 100000},
        {"symbol": "ETHUSDT", "quantity": 1.0, "buy_price": 4000},
        {"symbol": "BNBUSDT", "quantity": 5.0, "buy_price": 600}
    ]
    
    for pos in positions:
        try:
            response = requests.post(
                f"{base_url}/portfolio/add",
                params=pos
            )
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Added {pos['quantity']} {pos['symbol']} at ${pos['buy_price']:,}")
            else:
                print(f"   ❌ Failed to add {pos['symbol']}: {response.status_code}")
        except Exception as e:
            print(f"   ❌ Error adding {pos['symbol']}: {e}")
    
    # Test 3: Get portfolio summary
    print("\n3. Getting portfolio summary...")
    try:
        response = requests.get(f"{base_url}/portfolio")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Portfolio Summary:")
            print(f"      Total Invested: ${data.get('total_invested', 0):,.2f}")
            print(f"      Current Value: ${data.get('current_value', 0):,.2f}")
            print(f"      Total P&L: ${data.get('total_pnl', 0):,.2f}")
            print(f"      P&L %: {data.get('total_pnl_percentage', 0):.2f}%")
            print(f"      Positions: {len(data.get('positions', []))}")
            
            for pos in data.get('positions', []):
                print(f"        {pos['symbol']}: {pos['quantity']} @ ${pos['avg_buy_price']:,} = ${pos['current_value']:,.2f}")
        else:
            print(f"   ❌ Portfolio summary failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Portfolio summary error: {e}")

def test_alert_features():
    """Test alert management features"""
    base_url = "http://localhost:8000/api/v1"
    
    print("\n🧪 Testing Alert Management")
    print("=" * 50)
    
    # Test 1: Get alert types
    print("1. Getting alert types...")
    try:
        response = requests.get(f"{base_url}/alerts/types")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Available alert types: {len(data.get('alert_types', []))}")
            for alert_type in data.get('alert_types', []):
                print(f"      {alert_type['type']}: {alert_type['name']}")
        else:
            print(f"   ❌ Alert types failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Alert types error: {e}")
    
    # Test 2: Create alerts
    print("\n2. Creating alerts...")
    alerts = [
        {"symbol": "BTCUSDT", "alert_type": "price_above", "target_value": 120000, "message": "BTC above $120k!"},
        {"symbol": "BTCUSDT", "alert_type": "price_below", "target_value": 90000, "message": "BTC below $90k!"},
        {"symbol": "ETHUSDT", "alert_type": "rsi_overbought", "target_value": 70, "message": "ETH RSI overbought"},
        {"symbol": "BNBUSDT", "alert_type": "rsi_oversold", "target_value": 30, "message": "BNB RSI oversold"}
    ]
    
    created_alerts = []
    for alert in alerts:
        try:
            response = requests.post(
                f"{base_url}/alerts/create",
                params=alert
            )
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Created alert: {alert['alert_type']} for {alert['symbol']}")
                created_alerts.append(alert)
            else:
                print(f"   ❌ Failed to create alert for {alert['symbol']}: {response.status_code}")
        except Exception as e:
            print(f"   ❌ Error creating alert for {alert['symbol']}: {e}")
    
    # Test 3: Get alerts
    print("\n3. Getting alerts...")
    try:
        response = requests.get(f"{base_url}/alerts")
        if response.status_code == 200:
            data = response.json()
            alerts_list = data.get('alerts', [])
            print(f"   ✅ Found {len(alerts_list)} alerts")
            
            for alert in alerts_list:
                print(f"      ID {alert['id']}: {alert['symbol']} {alert['alert_type']} @ {alert['target_value']}")
                
        else:
            print(f"   ❌ Get alerts failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Get alerts error: {e}")
    
    # Test 4: Check alerts
    print("\n4. Checking alerts...")
    try:
        response = requests.post(f"{base_url}/alerts/check")
        if response.status_code == 200:
            data = response.json()
            triggered = data.get('triggered_alerts', [])
            print(f"   ✅ Checked alerts: {data.get('count', 0)} triggered")
            
            for alert in triggered:
                print(f"      🚨 TRIGGERED: {alert['symbol']} {alert['alert_type']}")
                
        else:
            print(f"   ❌ Check alerts failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Check alerts error: {e}")

def test_advanced_chart():
    """Test advanced chart data"""
    base_url = "http://localhost:8000/api/v1"
    
    print("\n🧪 Testing Advanced Chart Data")
    print("=" * 50)
    
    try:
        response = requests.get(f"{base_url}/advanced-chart/BTCUSDT?limit=10")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Advanced chart data loaded:")
            print(f"      Symbol: {data.get('symbol')}")
            print(f"      Timeframe: {data.get('timeframe')}")
            print(f"      Candlesticks: {len(data.get('candlesticks', []))}")
            print(f"      RSI data points: {len(data.get('rsi', []))}")
            print(f"      MACD data points: {len(data.get('macd', []))}")
            print(f"      Bollinger Bands: {len(data.get('bb_upper', []))}")
            
            if data.get('candlesticks'):
                latest = data['candlesticks'][-1]
                print(f"      Latest candle: ${latest['close']:,.2f}")
                
        else:
            print(f"   ❌ Advanced chart failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Advanced chart error: {e}")

def main():
    """Run all advanced feature tests"""
    print("🚀 Advanced Features Test Suite")
    print("Testing Portfolio Management, Alerts, and Advanced Charts")
    print("=" * 70)
    
    # Wait for server to be ready
    time.sleep(2)
    
    # Run tests
    test_portfolio_features()
    test_alert_features()
    test_advanced_chart()
    
    print("\n" + "=" * 70)
    print("🎉 Advanced features testing completed!")
    print("\n📊 Dashboard URLs:")
    print("   Main Dashboard: http://localhost:8000")
    print("   Advanced Charts: http://localhost:8000/static/advanced-dashboard.html")
    print("   API Docs: http://localhost:8000/docs")

if __name__ == "__main__":
    main()
