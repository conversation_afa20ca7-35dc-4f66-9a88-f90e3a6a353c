<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Management - Crypto Trading System</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #0d1421;
            color: #ffffff;
            overflow-x: hidden;
        }

        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .header h1 {
            font-size: 1.8em;
            font-weight: 600;
        }

        .header-nav {
            display: flex;
            gap: 15px;
        }

        .nav-btn {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.2);
        }

        .main-container {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .portfolio-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .summary-card {
            background: #131722;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .summary-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #00d4aa, #667eea);
        }

        .summary-value {
            font-size: 2.2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .summary-label {
            color: #888;
            font-size: 0.9em;
            margin-bottom: 10px;
        }

        .summary-change {
            font-size: 0.9em;
            padding: 4px 8px;
            border-radius: 4px;
        }

        .positive {
            color: #00d4aa;
            background: rgba(0, 212, 170, 0.1);
        }
        .negative {
            color: #ff4757;
            background: rgba(255, 71, 87, 0.1);
        }

        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .positions-section {
            background: #131722;
            border-radius: 10px;
            padding: 20px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .section-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #00d4aa;
        }

        .add-position-btn {
            background: #00d4aa;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: background 0.3s;
        }

        .add-position-btn:hover {
            background: #00b894;
        }

        .positions-table {
            width: 100%;
            border-collapse: collapse;
        }

        .positions-table th,
        .positions-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .positions-table th {
            color: #888;
            font-weight: 500;
            font-size: 0.9em;
        }

        .symbol-cell {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .symbol-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #00d4aa);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8em;
            font-weight: bold;
        }

        .pnl-cell {
            font-weight: 600;
        }

        .action-btn {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8em;
            margin: 0 2px;
        }

        .action-btn:hover {
            background: rgba(255,255,255,0.2);
        }

        .action-btn.danger {
            background: rgba(255, 71, 87, 0.2);
            border-color: #ff4757;
        }

        .action-btn.danger:hover {
            background: rgba(255, 71, 87, 0.3);
        }

        .charts-section {
            background: #131722;
            border-radius: 10px;
            padding: 20px;
        }

        .chart-container {
            height: 300px;
            margin-bottom: 20px;
        }

        .allocation-chart {
            height: 250px;
        }

        .quick-actions {
            background: #131722;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .action-card {
            background: rgba(255,255,255,0.05);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            border: 1px solid rgba(255,255,255,0.1);
        }

        .action-card:hover {
            background: rgba(255,255,255,0.1);
            transform: translateY(-2px);
        }

        .action-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
        }

        .modal-content {
            background: #131722;
            margin: 5% auto;
            padding: 30px;
            border-radius: 10px;
            width: 90%;
            max-width: 500px;
            position: relative;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            position: absolute;
            right: 15px;
            top: 15px;
        }

        .close:hover {
            color: white;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #888;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 6px;
            background: rgba(255,255,255,0.05);
            color: white;
            font-size: 14px;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #00d4aa;
        }

        .submit-btn {
            background: #00d4aa;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            width: 100%;
            font-size: 16px;
        }

        .submit-btn:hover {
            background: #00b894;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #888;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #888;
        }

        .empty-state-icon {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        @media (max-width: 1024px) {
            .content-grid {
                grid-template-columns: 1fr;
            }

            .portfolio-summary {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                gap: 10px;
            }

            .header-nav {
                flex-wrap: wrap;
                justify-content: center;
            }

            .main-container {
                padding: 10px;
            }

            .positions-table {
                font-size: 0.9em;
            }

            .positions-table th,
            .positions-table td {
                padding: 8px 4px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>💼 Portfolio Management</h1>
        <div class="header-nav">
            <a href="/" class="nav-btn">🏠 Dashboard</a>
            <a href="/static/advanced-dashboard.html" class="nav-btn">📊 Charts</a>
            <a href="/docs" class="nav-btn">📖 API</a>
            <button class="nav-btn" onclick="refreshData()">🔄 Refresh</button>
        </div>
    </div>

    <div class="main-container">
        <!-- Portfolio Summary -->
        <div class="portfolio-summary" id="portfolioSummary">
            <div class="loading">Loading portfolio summary...</div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <div class="section-header">
                <div class="section-title">Quick Actions</div>
            </div>
            <div class="actions-grid">
                <div class="action-card" onclick="openAddPositionModal()">
                    <div class="action-icon">➕</div>
                    <div>Add Position</div>
                </div>
                <div class="action-card" onclick="rebalancePortfolio()">
                    <div class="action-icon">⚖️</div>
                    <div>Rebalance</div>
                </div>
                <div class="action-card" onclick="exportPortfolio()">
                    <div class="action-icon">📊</div>
                    <div>Export Data</div>
                </div>
                <div class="action-card" onclick="viewAnalytics()">
                    <div class="action-icon">📈</div>
                    <div>Analytics</div>
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="content-grid">
            <!-- Positions Table -->
            <div class="positions-section">
                <div class="section-header">
                    <div class="section-title">Current Positions</div>
                    <button class="add-position-btn" onclick="openAddPositionModal()">+ Add Position</button>
                </div>
                <div id="positionsContainer">
                    <div class="loading">Loading positions...</div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="charts-section">
                <div class="section-header">
                    <div class="section-title">Portfolio Analytics</div>
                </div>

                <!-- Portfolio Performance Chart -->
                <div class="chart-container">
                    <div id="performanceChart" style="height: 100%;"></div>
                </div>

                <!-- Asset Allocation Chart -->
                <div class="allocation-chart">
                    <div id="allocationChart" style="height: 100%;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Position Modal -->
    <div id="addPositionModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeAddPositionModal()">&times;</span>
            <h2 style="margin-bottom: 20px; color: #00d4aa;">Add New Position</h2>
            <form id="addPositionForm">
                <div class="form-group">
                    <label for="symbol">Symbol</label>
                    <select id="symbol" required>
                        <option value="">Select Symbol</option>
                        <option value="BTCUSDT">BTC/USDT</option>
                        <option value="ETHUSDT">ETH/USDT</option>
                        <option value="BNBUSDT">BNB/USDT</option>
                        <option value="ADAUSDT">ADA/USDT</option>
                        <option value="XRPUSDT">XRP/USDT</option>
                        <option value="SOLUSDT">SOL/USDT</option>
                        <option value="DOTUSDT">DOT/USDT</option>
                        <option value="DOGEUSDT">DOGE/USDT</option>
                        <option value="AVAXUSDT">AVAX/USDT</option>
                        <option value="LINKUSDT">LINK/USDT</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="quantity">Quantity</label>
                    <input type="number" id="quantity" step="0.00000001" required placeholder="0.00000000">
                </div>
                <div class="form-group">
                    <label for="buyPrice">Buy Price (USDT)</label>
                    <input type="number" id="buyPrice" step="0.01" required placeholder="0.00">
                </div>
                <button type="submit" class="submit-btn">Add Position</button>
            </form>
        </div>
    </div>

    <script>
        let portfolioData = null;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadPortfolioData();

            // Setup form submission
            document.getElementById('addPositionForm').addEventListener('submit', handleAddPosition);
        });

        async function loadPortfolioData() {
            try {
                const response = await fetch('/api/v1/portfolio');
                portfolioData = await response.json();

                renderPortfolioSummary();
                renderPositionsTable();
                renderCharts();

            } catch (error) {
                console.error('Error loading portfolio data:', error);
                showError('Failed to load portfolio data');
            }
        }

        function renderPortfolioSummary() {
            if (!portfolioData) return;

            const summaryHtml = `
                <div class="summary-card">
                    <div class="summary-value">$${portfolioData.total_invested.toLocaleString()}</div>
                    <div class="summary-label">Total Invested</div>
                </div>
                <div class="summary-card">
                    <div class="summary-value">$${portfolioData.current_value.toLocaleString()}</div>
                    <div class="summary-label">Current Value</div>
                </div>
                <div class="summary-card">
                    <div class="summary-value ${portfolioData.total_pnl >= 0 ? 'positive' : 'negative'}">
                        $${portfolioData.total_pnl.toLocaleString()}
                    </div>
                    <div class="summary-label">Total P&L</div>
                    <div class="summary-change ${portfolioData.total_pnl_percentage >= 0 ? 'positive' : 'negative'}">
                        ${portfolioData.total_pnl_percentage >= 0 ? '+' : ''}${portfolioData.total_pnl_percentage.toFixed(2)}%
                    </div>
                </div>
                <div class="summary-card">
                    <div class="summary-value">${portfolioData.positions.length}</div>
                    <div class="summary-label">Active Positions</div>
                </div>
            `;

            document.getElementById('portfolioSummary').innerHTML = summaryHtml;
        }

        function renderPositionsTable() {
            if (!portfolioData || portfolioData.positions.length === 0) {
                document.getElementById('positionsContainer').innerHTML = `
                    <div class="empty-state">
                        <div class="empty-state-icon">📊</div>
                        <h3>No Positions Yet</h3>
                        <p>Add your first position to start tracking your portfolio</p>
                        <button class="add-position-btn" onclick="openAddPositionModal()" style="margin-top: 20px;">+ Add Position</button>
                    </div>
                `;
                return;
            }

            let tableHtml = `
                <table class="positions-table">
                    <thead>
                        <tr>
                            <th>Asset</th>
                            <th>Quantity</th>
                            <th>Avg Price</th>
                            <th>Current Price</th>
                            <th>Value</th>
                            <th>P&L</th>
                            <th>P&L %</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            portfolioData.positions.forEach(position => {
                const pnlClass = position.pnl >= 0 ? 'positive' : 'negative';
                const symbolShort = position.symbol.replace('USDT', '');

                tableHtml += `
                    <tr>
                        <td>
                            <div class="symbol-cell">
                                <div class="symbol-icon">${symbolShort.substring(0, 2)}</div>
                                <div>
                                    <div style="font-weight: 600;">${symbolShort}</div>
                                    <div style="font-size: 0.8em; color: #888;">${position.symbol}</div>
                                </div>
                            </div>
                        </td>
                        <td>${position.quantity.toFixed(8)}</td>
                        <td>$${position.avg_buy_price.toLocaleString()}</td>
                        <td>$${position.current_price.toLocaleString()}</td>
                        <td>$${position.current_value.toLocaleString()}</td>
                        <td class="pnl-cell ${pnlClass}">$${position.pnl.toLocaleString()}</td>
                        <td class="pnl-cell ${pnlClass}">${position.pnl_percentage >= 0 ? '+' : ''}${position.pnl_percentage.toFixed(2)}%</td>
                        <td>
                            <button class="action-btn" onclick="editPosition('${position.symbol}')">✏️</button>
                            <button class="action-btn danger" onclick="removePosition('${position.symbol}')">🗑️</button>
                        </td>
                    </tr>
                `;
            });

            tableHtml += `
                    </tbody>
                </table>
            `;

            document.getElementById('positionsContainer').innerHTML = tableHtml;
        }

        function renderCharts() {
            if (!portfolioData || portfolioData.positions.length === 0) {
                document.getElementById('performanceChart').innerHTML = '<div class="loading">No data to display</div>';
                document.getElementById('allocationChart').innerHTML = '<div class="loading">No data to display</div>';
                return;
            }

            // Portfolio Performance Chart (mock data for now)
            const performanceData = [{
                x: ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5', 'Day 6', 'Day 7'],
                y: [
                    portfolioData.total_invested,
                    portfolioData.total_invested * 1.02,
                    portfolioData.total_invested * 0.98,
                    portfolioData.total_invested * 1.05,
                    portfolioData.total_invested * 0.95,
                    portfolioData.total_invested * 1.03,
                    portfolioData.current_value
                ],
                type: 'scatter',
                mode: 'lines+markers',
                name: 'Portfolio Value',
                line: { color: '#00d4aa', width: 3 },
                marker: { color: '#00d4aa', size: 6 }
            }];

            const performanceLayout = {
                title: {
                    text: 'Portfolio Performance',
                    font: { color: '#ffffff', size: 14 }
                },
                paper_bgcolor: 'transparent',
                plot_bgcolor: 'transparent',
                font: { color: '#ffffff' },
                xaxis: {
                    gridcolor: 'rgba(255,255,255,0.1)',
                    color: '#ffffff'
                },
                yaxis: {
                    gridcolor: 'rgba(255,255,255,0.1)',
                    color: '#ffffff',
                    tickformat: '$,.0f'
                },
                margin: { l: 60, r: 20, t: 40, b: 40 },
                showlegend: false
            };

            Plotly.newPlot('performanceChart', performanceData, performanceLayout, {responsive: true, displayModeBar: false});

            // Asset Allocation Pie Chart
            const labels = portfolioData.positions.map(p => p.symbol.replace('USDT', ''));
            const values = portfolioData.positions.map(p => p.current_value);
            const colors = ['#00d4aa', '#667eea', '#ffa502', '#ff4757', '#5f27cd', '#00d2d3', '#ff9ff3', '#54a0ff', '#5f27cd', '#10ac84'];

            const allocationData = [{
                labels: labels,
                values: values,
                type: 'pie',
                marker: {
                    colors: colors.slice(0, labels.length)
                },
                textinfo: 'label+percent',
                textfont: { color: '#ffffff' },
                hovertemplate: '<b>%{label}</b><br>Value: $%{value:,.0f}<br>Share: %{percent}<extra></extra>'
            }];

            const allocationLayout = {
                title: {
                    text: 'Asset Allocation',
                    font: { color: '#ffffff', size: 14 }
                },
                paper_bgcolor: 'transparent',
                plot_bgcolor: 'transparent',
                font: { color: '#ffffff' },
                margin: { l: 20, r: 20, t: 40, b: 20 },
                showlegend: true,
                legend: {
                    orientation: 'v',
                    x: 1,
                    y: 0.5,
                    font: { color: '#ffffff', size: 10 }
                }
            };

            Plotly.newPlot('allocationChart', allocationData, allocationLayout, {responsive: true, displayModeBar: false});
        }

        // Modal functions
        function openAddPositionModal() {
            document.getElementById('addPositionModal').style.display = 'block';
        }

        function closeAddPositionModal() {
            document.getElementById('addPositionModal').style.display = 'none';
            document.getElementById('addPositionForm').reset();
        }

        // Form submission
        async function handleAddPosition(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const symbol = document.getElementById('symbol').value;
            const quantity = parseFloat(document.getElementById('quantity').value);
            const buyPrice = parseFloat(document.getElementById('buyPrice').value);

            try {
                const response = await fetch('/api/v1/portfolio/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        symbol: symbol,
                        quantity: quantity,
                        buy_price: buyPrice
                    })
                });

                if (response.ok) {
                    closeAddPositionModal();
                    await loadPortfolioData();
                    showSuccess('Position added successfully!');
                } else {
                    const error = await response.json();
                    showError(error.detail || 'Failed to add position');
                }
            } catch (error) {
                console.error('Error adding position:', error);
                showError('Failed to add position');
            }
        }

        // Action functions
        async function removePosition(symbol) {
            if (!confirm(`Are you sure you want to remove all ${symbol} positions?`)) {
                return;
            }

            try {
                const response = await fetch(`/api/v1/portfolio/remove?symbol=${symbol}`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    await loadPortfolioData();
                    showSuccess('Position removed successfully!');
                } else {
                    const error = await response.json();
                    showError(error.detail || 'Failed to remove position');
                }
            } catch (error) {
                console.error('Error removing position:', error);
                showError('Failed to remove position');
            }
        }

        function editPosition(symbol) {
            // For now, just show an alert. In a real app, you'd open an edit modal
            alert(`Edit functionality for ${symbol} coming soon!`);
        }

        function rebalancePortfolio() {
            alert('Portfolio rebalancing feature coming soon!');
        }

        function exportPortfolio() {
            if (!portfolioData || portfolioData.positions.length === 0) {
                showError('No portfolio data to export');
                return;
            }

            const csvContent = "data:text/csv;charset=utf-8,"
                + "Symbol,Quantity,Avg Buy Price,Current Price,Current Value,P&L,P&L %\n"
                + portfolioData.positions.map(p =>
                    `${p.symbol},${p.quantity},${p.avg_buy_price},${p.current_price},${p.current_value},${p.pnl},${p.pnl_percentage}`
                ).join("\n");

            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", "portfolio_export.csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showSuccess('Portfolio exported successfully!');
        }

        function viewAnalytics() {
            window.open('/static/advanced-dashboard.html', '_blank');
        }

        async function refreshData() {
            await loadPortfolioData();
            showSuccess('Data refreshed!');
        }

        // Utility functions
        function showSuccess(message) {
            // Simple success notification
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #00d4aa;
                color: white;
                padding: 15px 20px;
                border-radius: 6px;
                z-index: 10000;
                font-weight: 500;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                document.body.removeChild(notification);
            }, 3000);
        }

        function showError(message) {
            // Simple error notification
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #ff4757;
                color: white;
                padding: 15px 20px;
                border-radius: 6px;
                z-index: 10000;
                font-weight: 500;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                document.body.removeChild(notification);
            }, 3000);
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('addPositionModal');
            if (event.target == modal) {
                closeAddPositionModal();
            }
        }

        // Auto-refresh every 30 seconds
        setInterval(loadPortfolioData, 30000);
    </script>
</body>
</html>
