2025-05-27 01:56:36 - src.utils.logger - INFO - Logging initialized - Level: INFO, File: logs\crypto_system_20250527_015636.log
2025-05-27 01:56:57 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 01:57:02 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 01:57:34 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 01:57:52 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 01:57:55 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 01:57:55 - src.portfolio.manager - INFO - Added position: 0.1 BTCUSDT at $100000.0
2025-05-27 01:57:58 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 01:57:58 - src.portfolio.manager - INFO - Added position: 1.0 ETHUSDT at $4000.0
2025-05-27 01:58:00 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 01:58:00 - src.portfolio.manager - INFO - Added position: 5.0 BNBUSDT at $600.0
2025-05-27 01:58:02 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 01:58:06 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 01:58:08 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 01:58:08 - src.alerts.manager - INFO - Created alert: price_above for BTCUSDT at 120000.0
2025-05-27 01:58:11 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 01:58:11 - src.alerts.manager - INFO - Created alert: price_below for BTCUSDT at 90000.0
2025-05-27 01:58:13 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 01:58:13 - src.alerts.manager - INFO - Created alert: rsi_overbought for ETHUSDT at 70.0
2025-05-27 01:58:16 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 01:58:16 - src.alerts.manager - INFO - Created alert: rsi_oversold for BNBUSDT at 30.0
2025-05-27 01:58:18 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 01:58:20 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 01:58:34 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 01:59:08 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 01:59:17 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 01:59:34 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:00:34 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:01:34 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:02:34 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:03:34 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:04:34 - src.data_collection.binance_collector - INFO - Binance public client initialized
