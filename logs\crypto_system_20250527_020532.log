2025-05-27 02:05:32 - src.utils.logger - INFO - Logging initialized - Level: INFO, File: logs\crypto_system_20250527_020532.log
2025-05-27 02:05:34 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:05:43 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:05:54 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:05:56 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:06:00 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:06:25 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:06:34 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:06:43 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:06:47 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:06:47 - src.portfolio.manager - INFO - Added position: 1000.0 ADAUSDT at $1.5
2025-05-27 02:06:49 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:06:55 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:06:55 - src.portfolio.manager - INFO - Removed entire position: ADAUSDT
2025-05-27 02:06:55 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:07:25 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:07:34 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:07:55 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:08:25 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:08:34 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:08:55 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:09:09 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:09:18 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:09:34 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:09:43 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:10:14 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:10:34 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:10:44 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:10:45 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:10:47 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:10:57 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:11:14 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:11:25 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:11:34 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:11:44 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:11:55 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:12:14 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:12:25 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:12:34 - src.data_collection.binance_collector - INFO - Binance public client initialized
2025-05-27 02:12:44 - src.data_collection.binance_collector - INFO - Binance public client initialized
