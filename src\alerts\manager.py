"""
Price alert management system
"""
import logging
from typing import List, Dict, Optional
from datetime import datetime
from sqlalchemy.orm import Session
from src.database.database import get_db_session
from src.database.models import PriceAlert, CryptoPriceData
from src.data_collection.binance_collector import BinanceCollector
from src.technical_analysis.analyzer import TechnicalAnalyzer

logger = logging.getLogger(__name__)

class AlertManager:
    """Alert management class"""
    
    def __init__(self, user_id: str = "default"):
        self.user_id = user_id
        self.collector = BinanceCollector()
        self.analyzer = TechnicalAnalyzer()
    
    def create_alert(self, symbol: str, alert_type: str, target_value: float, message: str = None) -> bool:
        """Create a new price alert"""
        try:
            db = get_db_session()
            
            alert = PriceAlert(
                user_id=self.user_id,
                symbol=symbol.upper(),
                alert_type=alert_type,
                target_value=target_value,
                message=message or f"{alert_type} alert for {symbol} at {target_value}"
            )
            
            db.add(alert)
            db.commit()
            
            logger.info(f"Created alert: {alert_type} for {symbol} at {target_value}")
            return True
            
        except Exception as e:
            logger.error(f"Error creating alert: {e}")
            db.rollback()
            return False
        finally:
            db.close()
    
    def get_alerts(self, active_only: bool = True) -> List[Dict]:
        """Get all alerts for the user"""
        try:
            db = get_db_session()
            
            query = db.query(PriceAlert).filter(
                PriceAlert.user_id == self.user_id
            )
            
            if active_only:
                query = query.filter(PriceAlert.is_active == True)
            
            alerts = query.order_by(PriceAlert.created_at.desc()).all()
            
            alert_list = []
            for alert in alerts:
                alert_list.append({
                    "id": alert.id,
                    "symbol": alert.symbol,
                    "alert_type": alert.alert_type,
                    "target_value": alert.target_value,
                    "current_value": alert.current_value,
                    "is_active": alert.is_active,
                    "is_triggered": alert.is_triggered,
                    "message": alert.message,
                    "created_at": alert.created_at,
                    "triggered_at": alert.triggered_at
                })
            
            db.close()
            return alert_list
            
        except Exception as e:
            logger.error(f"Error getting alerts: {e}")
            return []
    
    def delete_alert(self, alert_id: int) -> bool:
        """Delete an alert"""
        try:
            db = get_db_session()
            
            alert = db.query(PriceAlert).filter(
                PriceAlert.id == alert_id,
                PriceAlert.user_id == self.user_id
            ).first()
            
            if alert:
                db.delete(alert)
                db.commit()
                logger.info(f"Deleted alert {alert_id}")
                return True
            else:
                logger.warning(f"Alert {alert_id} not found")
                return False
                
        except Exception as e:
            logger.error(f"Error deleting alert: {e}")
            db.rollback()
            return False
        finally:
            db.close()
    
    def toggle_alert(self, alert_id: int) -> bool:
        """Toggle alert active status"""
        try:
            db = get_db_session()
            
            alert = db.query(PriceAlert).filter(
                PriceAlert.id == alert_id,
                PriceAlert.user_id == self.user_id
            ).first()
            
            if alert:
                alert.is_active = not alert.is_active
                db.commit()
                logger.info(f"Toggled alert {alert_id} to {'active' if alert.is_active else 'inactive'}")
                return True
            else:
                logger.warning(f"Alert {alert_id} not found")
                return False
                
        except Exception as e:
            logger.error(f"Error toggling alert: {e}")
            db.rollback()
            return False
        finally:
            db.close()
    
    def check_alerts(self) -> List[Dict]:
        """Check all active alerts and trigger if conditions are met"""
        try:
            db = get_db_session()
            
            active_alerts = db.query(PriceAlert).filter(
                PriceAlert.is_active == True,
                PriceAlert.is_triggered == False
            ).all()
            
            triggered_alerts = []
            
            for alert in active_alerts:
                try:
                    current_value = None
                    triggered = False
                    
                    if alert.alert_type in ["price_above", "price_below"]:
                        # Price-based alerts
                        current_price = self.collector.get_current_price(alert.symbol)
                        if current_price:
                            current_value = current_price
                            
                            if alert.alert_type == "price_above" and current_price >= alert.target_value:
                                triggered = True
                            elif alert.alert_type == "price_below" and current_price <= alert.target_value:
                                triggered = True
                    
                    elif alert.alert_type in ["rsi_overbought", "rsi_oversold"]:
                        # RSI-based alerts
                        signals = self.analyzer.analyze_signals(alert.symbol)
                        if "rsi" in signals.get("signals", {}):
                            rsi_signal = signals["signals"]["rsi"]
                            
                            if alert.alert_type == "rsi_overbought" and rsi_signal == "OVERBOUGHT":
                                triggered = True
                                current_value = 70  # RSI threshold
                            elif alert.alert_type == "rsi_oversold" and rsi_signal == "OVERSOLD":
                                triggered = True
                                current_value = 30  # RSI threshold
                    
                    # Update current value
                    alert.current_value = current_value
                    
                    if triggered:
                        alert.is_triggered = True
                        alert.triggered_at = datetime.utcnow()
                        
                        triggered_alerts.append({
                            "id": alert.id,
                            "symbol": alert.symbol,
                            "alert_type": alert.alert_type,
                            "target_value": alert.target_value,
                            "current_value": current_value,
                            "message": alert.message,
                            "triggered_at": alert.triggered_at
                        })
                        
                        logger.info(f"Alert triggered: {alert.alert_type} for {alert.symbol}")
                
                except Exception as e:
                    logger.error(f"Error checking alert {alert.id}: {e}")
                    continue
            
            db.commit()
            db.close()
            
            return triggered_alerts
            
        except Exception as e:
            logger.error(f"Error checking alerts: {e}")
            return []
    
    def get_alert_types(self) -> List[Dict]:
        """Get available alert types"""
        return [
            {"type": "price_above", "name": "Price Above", "description": "Alert when price goes above target"},
            {"type": "price_below", "name": "Price Below", "description": "Alert when price goes below target"},
            {"type": "rsi_overbought", "name": "RSI Overbought", "description": "Alert when RSI indicates overbought (>70)"},
            {"type": "rsi_oversold", "name": "RSI Oversold", "description": "Alert when RSI indicates oversold (<30)"}
        ]
