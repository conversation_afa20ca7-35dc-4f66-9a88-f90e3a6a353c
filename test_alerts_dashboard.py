"""
Test script for alerts dashboard
"""
import requests
import json
import time

def test_alerts_dashboard():
    """Test alerts dashboard functionality"""
    base_url = "http://localhost:8000/api/v1"
    
    print("🧪 Testing Alerts Dashboard")
    print("=" * 50)
    
    # Test 1: Get current alerts
    print("1. Testing current alerts...")
    try:
        response = requests.get(f"{base_url}/alerts")
        if response.status_code == 200:
            data = response.json()
            alerts = data.get('alerts', [])
            print(f"   ✅ Alerts loaded successfully")
            print(f"   🚨 Total Alerts: {len(alerts)}")
            
            # Count by status
            active = sum(1 for a in alerts if a['is_active'] and not a['is_triggered'])
            triggered = sum(1 for a in alerts if a['is_triggered'])
            inactive = sum(1 for a in alerts if not a['is_active'])
            
            print(f"   📊 Active: {active}, Triggered: {triggered}, Inactive: {inactive}")
            
            # Show alerts
            for alert in alerts:
                status_emoji = "🚨" if alert['is_triggered'] else "✅" if alert['is_active'] else "⏸️"
                print(f"      {status_emoji} {alert['symbol']}: {alert['alert_type']} @ {alert['target_value']}")
                
        else:
            print(f"   ❌ Alerts failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Alerts error: {e}")
    
    # Test 2: Get alert types
    print("\n2. Testing alert types...")
    try:
        response = requests.get(f"{base_url}/alerts/types")
        if response.status_code == 200:
            data = response.json()
            alert_types = data.get('alert_types', [])
            print(f"   ✅ Alert types loaded: {len(alert_types)} types")
            
            for alert_type in alert_types:
                print(f"      📋 {alert_type['type']}: {alert_type['name']}")
                
        else:
            print(f"   ❌ Alert types failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Alert types error: {e}")
    
    # Test 3: Create a new alert
    print("\n3. Testing create alert...")
    try:
        new_alert = {
            "symbol": "BTCUSDT",
            "alert_type": "price_above",
            "target_value": 120000,
            "message": "BTC reached $120k!"
        }
        
        response = requests.post(
            f"{base_url}/alerts/create",
            params=new_alert
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Alert created: {new_alert['alert_type']} for {new_alert['symbol']} at ${new_alert['target_value']:,}")
        else:
            print(f"   ❌ Create alert failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Create alert error: {e}")
    
    # Test 4: Check alerts
    print("\n4. Testing check alerts...")
    try:
        response = requests.post(f"{base_url}/alerts/check")
        if response.status_code == 200:
            data = response.json()
            triggered_count = data.get('count', 0)
            triggered_alerts = data.get('triggered_alerts', [])
            
            print(f"   ✅ Alert check completed: {triggered_count} triggered")
            
            for alert in triggered_alerts:
                print(f"      🚨 TRIGGERED: {alert['symbol']} {alert['alert_type']}")
                
        else:
            print(f"   ❌ Check alerts failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Check alerts error: {e}")
    
    # Test 5: Get updated alerts
    print("\n5. Testing updated alerts...")
    try:
        response = requests.get(f"{base_url}/alerts")
        if response.status_code == 200:
            data = response.json()
            alerts = data.get('alerts', [])
            print(f"   ✅ Updated alerts: {len(alerts)} total")
            
            # Show latest alert
            if alerts:
                latest = alerts[0]  # Assuming sorted by creation date desc
                print(f"   📝 Latest: {latest['symbol']} {latest['alert_type']} @ {latest['target_value']}")
            
        else:
            print(f"   ❌ Updated alerts failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Updated alerts error: {e}")
    
    # Test 6: Test dashboard accessibility
    print("\n6. Testing dashboard accessibility...")
    try:
        response = requests.get("http://localhost:8000/static/alerts-dashboard.html")
        if response.status_code == 200:
            print(f"   ✅ Alerts dashboard accessible")
            print(f"   📄 Content length: {len(response.text):,} characters")
        else:
            print(f"   ❌ Dashboard not accessible: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Dashboard accessibility error: {e}")

def show_alerts_features():
    """Show alerts dashboard features"""
    print("\n🎯 Alerts Dashboard Features")
    print("=" * 50)
    
    features = [
        "🚨 Alert Summary - Active, triggered, and inactive counts",
        "📊 Alert Types - Price above/below, RSI overbought/oversold",
        "📋 Alert Management - Create, edit, delete, toggle alerts",
        "🔍 Alert Filtering - Filter by status (all, active, triggered, inactive)",
        "⚡ Quick Alerts - Pre-configured alert templates",
        "🚨 Triggered Alerts - Real-time notification of triggered alerts",
        "📱 Responsive Design - Mobile-friendly interface",
        "🔄 Auto Refresh - Real-time updates every 30 seconds",
        "🎨 Dark Theme - Professional trading interface",
        "📈 Integration - Works with technical analysis signals",
        "💬 Custom Messages - Personalized alert notifications",
        "🔔 Alert Checking - Manual and automatic alert monitoring"
    ]
    
    for feature in features:
        print(f"   {feature}")
    
    print("\n🌐 Dashboard URLs:")
    print("   Alerts: http://localhost:8000/static/alerts-dashboard.html")
    print("   Main Dashboard: http://localhost:8000")
    print("   Portfolio: http://localhost:8000/static/portfolio-dashboard.html")
    print("   Advanced Charts: http://localhost:8000/static/advanced-dashboard.html")

def show_alert_types():
    """Show available alert types"""
    print("\n📋 Available Alert Types")
    print("=" * 50)
    
    alert_types = [
        {
            "type": "price_above",
            "name": "Price Above",
            "description": "Triggers when price goes above target value",
            "example": "BTC above $120,000"
        },
        {
            "type": "price_below", 
            "name": "Price Below",
            "description": "Triggers when price goes below target value",
            "example": "BTC below $90,000"
        },
        {
            "type": "rsi_overbought",
            "name": "RSI Overbought", 
            "description": "Triggers when RSI indicates overbought condition (>70)",
            "example": "ETH RSI > 70"
        },
        {
            "type": "rsi_oversold",
            "name": "RSI Oversold",
            "description": "Triggers when RSI indicates oversold condition (<30)", 
            "example": "BNB RSI < 30"
        }
    ]
    
    for alert_type in alert_types:
        print(f"   📊 {alert_type['name']} ({alert_type['type']})")
        print(f"      {alert_type['description']}")
        print(f"      Example: {alert_type['example']}")
        print()

def main():
    """Run alerts dashboard tests"""
    print("🚀 Alerts Dashboard Test Suite")
    print("Testing alert management functionality")
    print("=" * 70)
    
    # Wait for server to be ready
    time.sleep(2)
    
    # Run tests
    test_alerts_dashboard()
    show_alerts_features()
    show_alert_types()
    
    print("\n" + "=" * 70)
    print("🎉 Alerts dashboard testing completed!")
    print("\n💡 Next Steps:")
    print("   1. Open http://localhost:8000/static/alerts-dashboard.html")
    print("   2. Create alerts using the + Create Alert button")
    print("   3. Use Quick Alerts for common alert types")
    print("   4. Monitor triggered alerts in real-time")
    print("   5. Filter alerts by status (active, triggered, inactive)")

if __name__ == "__main__":
    main()
