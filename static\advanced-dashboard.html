<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Crypto Trading Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #0d1421;
            color: #ffffff;
            overflow-x: hidden;
        }

        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .header h1 {
            font-size: 1.8em;
            font-weight: 600;
        }

        .header-controls {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .symbol-selector {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
        }

        .refresh-btn {
            background: #00d4aa;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: background 0.3s;
        }

        .refresh-btn:hover {
            background: #00b894;
        }

        .main-container {
            display: grid;
            grid-template-columns: 1fr 300px;
            height: calc(100vh - 70px);
            gap: 10px;
            padding: 10px;
        }

        .chart-container {
            background: #131722;
            border-radius: 8px;
            padding: 15px;
            display: flex;
            flex-direction: column;
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .chart-title {
            font-size: 1.4em;
            font-weight: 600;
        }

        .timeframe-buttons {
            display: flex;
            gap: 5px;
        }

        .timeframe-btn {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            color: white;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }

        .timeframe-btn.active {
            background: #00d4aa;
            border-color: #00d4aa;
        }

        .timeframe-btn:hover {
            background: rgba(255,255,255,0.2);
        }

        .chart-area {
            flex: 1;
            min-height: 400px;
        }

        .sidebar {
            background: #131722;
            border-radius: 8px;
            padding: 15px;
            overflow-y: auto;
        }

        .sidebar-section {
            margin-bottom: 25px;
        }

        .sidebar-title {
            font-size: 1.1em;
            font-weight: 600;
            margin-bottom: 10px;
            color: #00d4aa;
        }

        .price-info {
            background: rgba(255,255,255,0.05);
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 15px;
        }

        .current-price {
            font-size: 1.8em;
            font-weight: bold;
            color: #00d4aa;
        }

        .price-change {
            font-size: 0.9em;
            margin-top: 5px;
        }

        .positive { color: #00d4aa; }
        .negative { color: #ff4757; }

        .signals-grid {
            display: grid;
            gap: 8px;
        }

        .signal-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 12px;
            background: rgba(255,255,255,0.05);
            border-radius: 4px;
            font-size: 0.9em;
        }

        .signal-value {
            font-weight: 600;
        }

        .signal-bullish { border-left: 3px solid #00d4aa; }
        .signal-bearish { border-left: 3px solid #ff4757; }
        .signal-neutral { border-left: 3px solid #ffa502; }
        .signal-overbought { border-left: 3px solid #ff6b6b; }
        .signal-oversold { border-left: 3px solid #4834d4; }

        .indicators-panel {
            background: rgba(255,255,255,0.05);
            padding: 12px;
            border-radius: 6px;
        }

        .indicator-row {
            display: flex;
            justify-content: space-between;
            padding: 6px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .indicator-row:last-child {
            border-bottom: none;
        }

        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            color: #888;
        }

        .error {
            color: #ff4757;
            text-align: center;
            padding: 20px;
        }

        @media (max-width: 1024px) {
            .main-container {
                grid-template-columns: 1fr;
                grid-template-rows: 1fr auto;
            }

            .sidebar {
                max-height: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Advanced Crypto Trading Dashboard</h1>
        <div class="header-controls">
            <select class="symbol-selector" id="symbolSelector">
                <option value="BTCUSDT">BTC/USDT</option>
                <option value="ETHUSDT">ETH/USDT</option>
                <option value="BNBUSDT">BNB/USDT</option>
                <option value="ADAUSDT">ADA/USDT</option>
                <option value="XRPUSDT">XRP/USDT</option>
                <option value="SOLUSDT">SOL/USDT</option>
                <option value="DOTUSDT">DOT/USDT</option>
                <option value="DOGEUSDT">DOGE/USDT</option>
                <option value="AVAXUSDT">AVAX/USDT</option>
                <option value="LINKUSDT">LINK/USDT</option>
            </select>
            <button class="refresh-btn" onclick="loadData()">🔄 Refresh</button>
        </div>
    </div>

    <div class="main-container">
        <div class="chart-container">
            <div class="chart-header">
                <div class="chart-title" id="chartTitle">BTC/USDT</div>
                <div class="timeframe-buttons">
                    <button class="timeframe-btn active" data-timeframe="1m">1m</button>
                    <button class="timeframe-btn" data-timeframe="5m">5m</button>
                    <button class="timeframe-btn" data-timeframe="15m">15m</button>
                    <button class="timeframe-btn" data-timeframe="1h">1h</button>
                    <button class="timeframe-btn" data-timeframe="4h">4h</button>
                    <button class="timeframe-btn" data-timeframe="1d">1d</button>
                </div>
            </div>
            <div class="chart-area" id="chartArea">
                <div class="loading">Loading chart data...</div>
            </div>
        </div>

        <div class="sidebar">
            <div class="sidebar-section">
                <div class="sidebar-title">Price Info</div>
                <div class="price-info" id="priceInfo">
                    <div class="loading">Loading price...</div>
                </div>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-title">Trading Signals</div>
                <div class="signals-grid" id="signalsGrid">
                    <div class="loading">Loading signals...</div>
                </div>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-title">Technical Indicators</div>
                <div class="indicators-panel" id="indicatorsPanel">
                    <div class="loading">Loading indicators...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentSymbol = 'BTCUSDT';
        let currentTimeframe = '1m';
        let chartData = null;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            loadData();
        });

        function setupEventListeners() {
            // Symbol selector
            document.getElementById('symbolSelector').addEventListener('change', function(e) {
                currentSymbol = e.target.value;
                document.getElementById('chartTitle').textContent = currentSymbol;
                loadData();
            });

            // Timeframe buttons
            document.querySelectorAll('.timeframe-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.timeframe-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    currentTimeframe = this.dataset.timeframe;
                    loadChartData();
                });
            });
        }

        async function loadData() {
            await Promise.all([
                loadPriceInfo(),
                loadSignals(),
                loadChartData()
            ]);
        }

        async function loadPriceInfo() {
            try {
                const response = await fetch(`/api/v1/price/${currentSymbol}?limit=1`);
                const data = await response.json();

                if (data && data.length > 0) {
                    const latest = data[0];
                    const priceChange = ((latest.close - latest.open) / latest.open * 100).toFixed(2);
                    const changeClass = priceChange >= 0 ? 'positive' : 'negative';
                    const changeSign = priceChange >= 0 ? '+' : '';

                    document.getElementById('priceInfo').innerHTML = `
                        <div class="current-price">$${latest.close.toLocaleString()}</div>
                        <div class="price-change ${changeClass}">
                            ${changeSign}${priceChange}% (24h)
                        </div>
                        <div style="margin-top: 10px; font-size: 0.8em; color: #888;">
                            H: $${latest.high.toLocaleString()} | L: $${latest.low.toLocaleString()}
                        </div>
                        <div style="font-size: 0.8em; color: #888;">
                            Vol: ${(latest.volume / 1000000).toFixed(2)}M
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error loading price info:', error);
                document.getElementById('priceInfo').innerHTML = '<div class="error">Error loading price</div>';
            }
        }

        async function loadSignals() {
            try {
                const response = await fetch(`/api/v1/signals/${currentSymbol}`);
                const data = await response.json();

                if (data && data.signals) {
                    let signalsHtml = '';

                    for (const [indicator, signal] of Object.entries(data.signals)) {
                        let signalClass = 'signal-neutral';
                        if (signal === 'BULLISH') signalClass = 'signal-bullish';
                        else if (signal === 'BEARISH') signalClass = 'signal-bearish';
                        else if (signal === 'OVERBOUGHT') signalClass = 'signal-overbought';
                        else if (signal === 'OVERSOLD') signalClass = 'signal-oversold';

                        signalsHtml += `
                            <div class="signal-item ${signalClass}">
                                <span>${indicator.toUpperCase()}</span>
                                <span class="signal-value">${signal}</span>
                            </div>
                        `;
                    }

                    document.getElementById('signalsGrid').innerHTML = signalsHtml;
                }
            } catch (error) {
                console.error('Error loading signals:', error);
                document.getElementById('signalsGrid').innerHTML = '<div class="error">Error loading signals</div>';
            }
        }

        async function loadChartData() {
            try {
                document.getElementById('chartArea').innerHTML = '<div class="loading">Loading chart data...</div>';

                const response = await fetch(`/api/v1/advanced-chart/${currentSymbol}?timeframe=${currentTimeframe}&limit=500`);
                const data = await response.json();

                if (data && data.candlesticks) {
                    chartData = data;
                    renderChart();
                    updateIndicators();
                }
            } catch (error) {
                console.error('Error loading chart data:', error);
                document.getElementById('chartArea').innerHTML = '<div class="error">Error loading chart data</div>';
            }
        }

        function renderChart() {
            if (!chartData) return;

            const candlesticks = chartData.candlesticks;

            // Prepare candlestick data
            const timestamps = candlesticks.map(c => c.timestamp);
            const opens = candlesticks.map(c => c.open);
            const highs = candlesticks.map(c => c.high);
            const lows = candlesticks.map(c => c.low);
            const closes = candlesticks.map(c => c.close);
            const volumes = candlesticks.map(c => c.volume);

            // Prepare indicator data
            const rsiValues = chartData.rsi.map(r => r.value);
            const macdValues = chartData.macd.map(m => m.value);
            const macdSignalValues = chartData.macd_signal.map(m => m.value);
            const bbUpper = chartData.bb_upper.map(b => b.value);
            const bbMiddle = chartData.bb_middle.map(b => b.value);
            const bbLower = chartData.bb_lower.map(b => b.value);

            // Create traces
            const traces = [
                // Candlestick chart
                {
                    x: timestamps,
                    open: opens,
                    high: highs,
                    low: lows,
                    close: closes,
                    type: 'candlestick',
                    name: currentSymbol,
                    increasing: { line: { color: '#00d4aa' } },
                    decreasing: { line: { color: '#ff4757' } },
                    xaxis: 'x',
                    yaxis: 'y'
                },
                // Bollinger Bands
                {
                    x: timestamps,
                    y: bbUpper,
                    type: 'scatter',
                    mode: 'lines',
                    name: 'BB Upper',
                    line: { color: 'rgba(255, 165, 0, 0.5)', width: 1 },
                    xaxis: 'x',
                    yaxis: 'y'
                },
                {
                    x: timestamps,
                    y: bbMiddle,
                    type: 'scatter',
                    mode: 'lines',
                    name: 'BB Middle',
                    line: { color: 'rgba(255, 165, 0, 0.8)', width: 1 },
                    xaxis: 'x',
                    yaxis: 'y'
                },
                {
                    x: timestamps,
                    y: bbLower,
                    type: 'scatter',
                    mode: 'lines',
                    name: 'BB Lower',
                    line: { color: 'rgba(255, 165, 0, 0.5)', width: 1 },
                    fill: 'tonexty',
                    fillcolor: 'rgba(255, 165, 0, 0.1)',
                    xaxis: 'x',
                    yaxis: 'y'
                },
                // Volume
                {
                    x: timestamps,
                    y: volumes,
                    type: 'bar',
                    name: 'Volume',
                    marker: { color: 'rgba(0, 212, 170, 0.3)' },
                    xaxis: 'x',
                    yaxis: 'y2'
                },
                // RSI
                {
                    x: timestamps,
                    y: rsiValues,
                    type: 'scatter',
                    mode: 'lines',
                    name: 'RSI',
                    line: { color: '#4834d4', width: 2 },
                    xaxis: 'x',
                    yaxis: 'y3'
                },
                // MACD
                {
                    x: timestamps,
                    y: macdValues,
                    type: 'scatter',
                    mode: 'lines',
                    name: 'MACD',
                    line: { color: '#00d4aa', width: 2 },
                    xaxis: 'x',
                    yaxis: 'y4'
                },
                {
                    x: timestamps,
                    y: macdSignalValues,
                    type: 'scatter',
                    mode: 'lines',
                    name: 'MACD Signal',
                    line: { color: '#ff4757', width: 2 },
                    xaxis: 'x',
                    yaxis: 'y4'
                }
            ];

            // Layout configuration
            const layout = {
                title: {
                    text: `${currentSymbol} - ${currentTimeframe}`,
                    font: { color: '#ffffff', size: 16 }
                },
                paper_bgcolor: '#131722',
                plot_bgcolor: '#131722',
                font: { color: '#ffffff' },
                showlegend: true,
                legend: {
                    orientation: 'h',
                    x: 0,
                    y: 1.02,
                    font: { color: '#ffffff', size: 10 }
                },
                grid: {
                    rows: 4,
                    columns: 1,
                    subplots: [['xy'], ['xy2'], ['xy3'], ['xy4']],
                    roworder: 'top to bottom'
                },
                xaxis: {
                    gridcolor: 'rgba(255,255,255,0.1)',
                    showgrid: true,
                    zeroline: false,
                    color: '#ffffff'
                },
                yaxis: {
                    domain: [0.6, 1],
                    gridcolor: 'rgba(255,255,255,0.1)',
                    showgrid: true,
                    zeroline: false,
                    color: '#ffffff',
                    title: 'Price'
                },
                yaxis2: {
                    domain: [0.45, 0.55],
                    gridcolor: 'rgba(255,255,255,0.1)',
                    showgrid: true,
                    zeroline: false,
                    color: '#ffffff',
                    title: 'Volume'
                },
                yaxis3: {
                    domain: [0.25, 0.4],
                    gridcolor: 'rgba(255,255,255,0.1)',
                    showgrid: true,
                    zeroline: false,
                    color: '#ffffff',
                    title: 'RSI',
                    range: [0, 100]
                },
                yaxis4: {
                    domain: [0, 0.2],
                    gridcolor: 'rgba(255,255,255,0.1)',
                    showgrid: true,
                    zeroline: false,
                    color: '#ffffff',
                    title: 'MACD'
                },
                margin: { l: 60, r: 20, t: 60, b: 40 }
            };

            // Plot configuration
            const config = {
                responsive: true,
                displayModeBar: true,
                modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],
                displaylogo: false
            };

            Plotly.newPlot('chartArea', traces, layout, config);
        }

        function updateIndicators() {
            if (!chartData || !chartData.candlesticks.length) return;

            const latest = chartData.candlesticks[chartData.candlesticks.length - 1];
            const latestRSI = chartData.rsi[chartData.rsi.length - 1]?.value;
            const latestMACD = chartData.macd[chartData.macd.length - 1]?.value;
            const latestMACDSignal = chartData.macd_signal[chartData.macd_signal.length - 1]?.value;
            const latestBBUpper = chartData.bb_upper[chartData.bb_upper.length - 1]?.value;
            const latestBBLower = chartData.bb_lower[chartData.bb_lower.length - 1]?.value;

            const indicatorsHtml = `
                <div class="indicator-row">
                    <span>RSI (14)</span>
                    <span>${latestRSI ? latestRSI.toFixed(2) : 'N/A'}</span>
                </div>
                <div class="indicator-row">
                    <span>MACD</span>
                    <span>${latestMACD ? latestMACD.toFixed(4) : 'N/A'}</span>
                </div>
                <div class="indicator-row">
                    <span>MACD Signal</span>
                    <span>${latestMACDSignal ? latestMACDSignal.toFixed(4) : 'N/A'}</span>
                </div>
                <div class="indicator-row">
                    <span>BB Upper</span>
                    <span>${latestBBUpper ? '$' + latestBBUpper.toLocaleString() : 'N/A'}</span>
                </div>
                <div class="indicator-row">
                    <span>BB Lower</span>
                    <span>${latestBBLower ? '$' + latestBBLower.toLocaleString() : 'N/A'}</span>
                </div>
                <div class="indicator-row">
                    <span>Volume</span>
                    <span>${(latest.volume / 1000000).toFixed(2)}M</span>
                </div>
            `;

            document.getElementById('indicatorsPanel').innerHTML = indicatorsHtml;
        }

        // Auto-refresh every 30 seconds
        setInterval(loadData, 30000);
    </script>
</body>
</html>
